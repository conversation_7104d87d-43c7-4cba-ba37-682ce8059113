#!/usr/bin/env python3
"""
Quick Province Analysis - Key Code Snippets
===========================================

This script contains the essential code for analyzing Ocrevus discontinuation 
patterns by province. Clean, concise, and focused on the core analysis.
"""

import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns

def main():
    """Main analysis function with key insights."""
    
    print("OCREVUS PROVINCE DISCONTINUATION ANALYSIS")
    print("=" * 50)
    
    # 1. Load and prepare data
    df = pd.read_csv('Ocrevus_switch_alerts.csv')
    
    # 2. Create discontinuation status
    df['is_discontinued'] = df['patientsubstatus'].str.contains('Discontinued', na=False)
    
    # 3. Calculate discontinuation rates by province
    province_stats = df.groupby('patientprovince').agg({
        'patientid': 'count',
        'is_discontinued': ['sum', 'mean']
    }).round(3)
    
    # Flatten column names
    province_stats.columns = ['total_patients', 'discontinued_count', 'discontinuation_rate']
    province_stats['discontinuation_rate'] *= 100  # Convert to percentage
    
    # 4. Sort by discontinuation rate
    province_stats = province_stats.sort_values('discontinuation_rate', ascending=False)
    
    # 5. Display results
    overall_rate = df['is_discontinued'].mean() * 100
    print(f"\nOverall discontinuation rate: {overall_rate:.1f}%")
    print(f"\nProvince Rankings (Highest to Lowest Risk):")
    print("-" * 60)
    print(f"{'Province':<10} {'Total':<8} {'Discontinued':<12} {'Rate (%)':<10}")
    print("-" * 60)
    
    for province, row in province_stats.iterrows():
        print(f"{province:<10} {row['total_patients']:<8} {row['discontinued_count']:<12} {row['discontinuation_rate']:<10.1f}")
    
    # 6. Identify high-risk provinces
    high_risk_threshold = overall_rate + 5
    high_risk_provinces = province_stats[province_stats['discontinuation_rate'] > high_risk_threshold]
    
    print(f"\n🚨 HIGH-RISK PROVINCES (>{high_risk_threshold:.1f}%):")
    for province, row in high_risk_provinces.iterrows():
        print(f"   • {province}: {row['discontinuation_rate']:.1f}% ({row['discontinued_count']:.0f}/{row['total_patients']:.0f} patients)")
    
    # 7. Create simple visualization
    try:
        plt.figure(figsize=(12, 6))
        
        # Bar chart of discontinuation rates
        colors = ['red' if rate > overall_rate else 'steelblue' 
                 for rate in province_stats['discontinuation_rate']]
        
        bars = plt.bar(province_stats.index, province_stats['discontinuation_rate'], 
                      color=colors, alpha=0.7, edgecolor='black')
        
        # Add national average line
        plt.axhline(y=overall_rate, color='orange', linestyle='--', linewidth=2, 
                   label=f'National Average ({overall_rate:.1f}%)')
        
        # Formatting
        plt.title('Ocrevus Discontinuation Rates by Province', fontsize=14, fontweight='bold')
        plt.xlabel('Province')
        plt.ylabel('Discontinuation Rate (%)')
        plt.legend()
        plt.grid(axis='y', alpha=0.3)
        plt.xticks(rotation=45)
        
        # Add value labels on bars
        for bar, rate in zip(bars, province_stats['discontinuation_rate']):
            plt.text(bar.get_x() + bar.get_width()/2., bar.get_height() + 0.5,
                    f'{rate:.1f}%', ha='center', va='bottom', fontsize=9)
        
        plt.tight_layout()
        plt.savefig('quick_province_analysis.png', dpi=200, bbox_inches='tight')
        plt.show()
        
        print(f"\n✓ Visualization saved as 'quick_province_analysis.png'")
        
    except Exception as e:
        print(f"⚠ Could not create visualization: {e}")
    
    # 8. Key insights summary
    print(f"\n📊 KEY INSIGHTS:")
    print(f"   • Highest risk: {province_stats.index[0]} ({province_stats.iloc[0]['discontinuation_rate']:.1f}%)")
    print(f"   • Lowest risk: {province_stats.index[-1]} ({province_stats.iloc[-1]['discontinuation_rate']:.1f}%)")
    print(f"   • Rate spread: {province_stats['discontinuation_rate'].max() - province_stats['discontinuation_rate'].min():.1f} percentage points")
    print(f"   • Provinces above average: {len(province_stats[province_stats['discontinuation_rate'] > overall_rate])}")
    
    return province_stats

# Additional utility functions for deeper analysis

def analyze_demographics_by_province(df, provinces_of_interest):
    """Analyze age and gender patterns for specific provinces."""
    
    print(f"\nDEMOGRAPHIC ANALYSIS FOR: {', '.join(provinces_of_interest)}")
    print("-" * 50)
    
    for province in provinces_of_interest:
        province_data = df[df['patientprovince'] == province]
        
        if len(province_data) > 0:
            print(f"\n{province}:")
            
            # Age analysis
            active_age = province_data[~province_data['is_discontinued']]['age'].mean()
            disc_age = province_data[province_data['is_discontinued']]['age'].mean()
            
            print(f"   Average age - Active: {active_age:.1f}, Discontinued: {disc_age:.1f}")
            
            # Gender analysis
            gender_disc = province_data[province_data['is_discontinued']]['gender'].value_counts(normalize=True) * 100
            print(f"   Discontinued by gender: {dict(gender_disc.round(1))}")

def quick_discontinuation_reasons(df, province):
    """Show top discontinuation reasons for a specific province."""
    
    province_disc = df[(df['patientprovince'] == province) & (df['is_discontinued'])]
    
    if len(province_disc) > 0:
        print(f"\nTOP DISCONTINUATION REASONS - {province}:")
        reasons = province_disc['patientsubstatus'].value_counts().head(5)
        
        for reason, count in reasons.items():
            pct = (count / len(province_disc)) * 100
            print(f"   • {reason}: {count} ({pct:.1f}%)")
    else:
        print(f"\nNo discontinuation data available for {province}")

if __name__ == "__main__":
    # Run main analysis
    results = main()
    
    # Additional analysis for high-risk provinces
    high_risk = ['BC', 'NS']  # Based on main analysis results
    
    if len(high_risk) > 0:
        print(f"\n" + "="*60)
        print("DETAILED ANALYSIS FOR HIGH-RISK PROVINCES")
        print("="*60)
        
        # Load data again for additional analysis
        df = pd.read_csv('Ocrevus_switch_alerts.csv')
        df['is_discontinued'] = df['patientsubstatus'].str.contains('Discontinued', na=False)
        
        # Demographic analysis
        analyze_demographics_by_province(df, high_risk)
        
        # Discontinuation reasons
        for province in high_risk:
            quick_discontinuation_reasons(df, province)
    
    print(f"\n" + "="*60)
    print("ANALYSIS COMPLETE")
    print("="*60)
    print("✓ Province discontinuation analysis completed")
    print("✓ Focus on high-risk provinces for immediate intervention")
    print("✓ Monitor trends and implement targeted retention strategies")
