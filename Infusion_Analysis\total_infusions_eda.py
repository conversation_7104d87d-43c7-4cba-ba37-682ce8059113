"""
Exploratory Data Analysis: Total Infusions vs Patient Discontinuation Status
===========================================================================

This script performs a comprehensive exploratory data analysis to understand
the relationship between total_infusions and patientsubstatus in the Ocrevus
therapy dataset, specifically focusing on how the number of infusions might
correlate with patient discontinuation.

Author: AI Assistant
Date: 2025
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

def load_and_preprocess_data(file_path='Ocrevus_switch_alerts.csv'):
    """
    Load the dataset and perform initial preprocessing.

    Parameters:
    -----------
    file_path : str
        Path to the CSV file containing the Ocrevus data

    Returns:
    --------
    pd.DataFrame
        Preprocessed dataframe with clean total_infusions and status columns
    """
    print("=" * 70)
    print("OCREVUS THERAPY: TOTAL INFUSIONS vs DISCONTINUATION ANALYSIS")
    print("=" * 70)

    # Load the dataset
    try:
        df = pd.read_csv(file_path)
        print(f"✓ Dataset loaded successfully: {df.shape[0]:,} rows × {df.shape[1]} columns")
    except FileNotFoundError:
        print(f"❌ Error: File '{file_path}' not found.")
        return None

    # Check for required columns
    required_cols = ['total_infusions', 'patientsubstatus']
    missing_cols = [col for col in required_cols if col not in df.columns]
    if missing_cols:
        print(f"❌ Error: Missing required columns: {missing_cols}")
        return None

    print(f"✓ Required columns found: {required_cols}")

    # Data preprocessing
    print("\n" + "-" * 50)
    print("DATA PREPROCESSING")
    print("-" * 50)

    # 1. Ensure total_infusions is numeric
    original_dtype = df['total_infusions'].dtype
    print(f"Original total_infusions data type: {original_dtype}")

    # Convert to numeric, handling any non-numeric values
    df['total_infusions'] = pd.to_numeric(df['total_infusions'], errors='coerce')

    # Check for missing values after conversion
    missing_infusions = df['total_infusions'].isna().sum()
    if missing_infusions > 0:
        print(f"⚠️  Warning: {missing_infusions} missing values in total_infusions after conversion")
        # Remove rows with missing total_infusions
        df = df.dropna(subset=['total_infusions'])
        print(f"✓ Removed rows with missing total_infusions. New shape: {df.shape}")
    else:
        print("✓ No missing values in total_infusions")

    # 2. Simplify patientsubstatus to Active/Discontinued
    print(f"\nOriginal patientsubstatus categories: {df['patientsubstatus'].nunique()}")
    print("Categories:", df['patientsubstatus'].unique()[:10], "..." if df['patientsubstatus'].nunique() > 10 else "")

    # Create simplified status
    df['patient_status'] = df['patientsubstatus'].apply(
        lambda x: 'Discontinued' if 'Discontinued' in str(x) else 'Active'
    )

    print(f"✓ Simplified to binary status:")
    status_counts = df['patient_status'].value_counts()
    for status, count in status_counts.items():
        percentage = (count / len(df)) * 100
        print(f"   • {status}: {count:,} patients ({percentage:.1f}%)")

    # 3. Basic data validation
    print(f"\n✓ Final dataset shape: {df.shape}")
    print(f"✓ Total infusions range: {df['total_infusions'].min():.0f} - {df['total_infusions'].max():.0f}")

    return df

def analyze_total_infusions_distribution(df):
    """
    Analyze the overall distribution of total_infusions.

    Parameters:
    -----------
    df : pd.DataFrame
        The preprocessed dataframe
    """
    print("\n" + "-" * 50)
    print("TOTAL INFUSIONS: OVERALL DISTRIBUTION ANALYSIS")
    print("-" * 50)

    # Calculate descriptive statistics
    stats_overall = df['total_infusions'].describe()

    print("📊 DESCRIPTIVE STATISTICS:")
    print(f"   • Count:          {stats_overall['count']:>8.0f}")
    print(f"   • Mean:           {stats_overall['mean']:>8.2f}")
    print(f"   • Median:         {stats_overall['50%']:>8.2f}")
    print(f"   • Standard Dev:   {stats_overall['std']:>8.2f}")
    print(f"   • Minimum:        {stats_overall['min']:>8.0f}")
    print(f"   • Maximum:        {stats_overall['max']:>8.0f}")
    print(f"   • 25th Percentile:{stats_overall['25%']:>8.2f}")
    print(f"   • 75th Percentile:{stats_overall['75%']:>8.2f}")

    # Additional statistics
    mode_val = df['total_infusions'].mode().iloc[0] if not df['total_infusions'].mode().empty else "N/A"
    skewness = df['total_infusions'].skew()
    kurtosis = df['total_infusions'].kurtosis()

    print(f"\n📈 DISTRIBUTION CHARACTERISTICS:")
    print(f"   • Mode:           {mode_val:>8}")
    print(f"   • Skewness:       {skewness:>8.3f} ({'Right-skewed' if skewness > 0 else 'Left-skewed' if skewness < 0 else 'Symmetric'})")
    print(f"   • Kurtosis:       {kurtosis:>8.3f} ({'Heavy-tailed' if kurtosis > 0 else 'Light-tailed'})")

    # Infusion ranges analysis
    print(f"\n🎯 INFUSION RANGES:")
    ranges = [
        (1, 3, "Early therapy (1-3 infusions)"),
        (4, 6, "Short-term therapy (4-6 infusions)"),
        (7, 12, "Medium-term therapy (7-12 infusions)"),
        (13, float('inf'), "Long-term therapy (13+ infusions)")
    ]

    for min_inf, max_inf, label in ranges:
        if max_inf == float('inf'):
            count = len(df[df['total_infusions'] >= min_inf])
        else:
            count = len(df[(df['total_infusions'] >= min_inf) & (df['total_infusions'] <= max_inf)])
        percentage = (count / len(df)) * 100
        print(f"   • {label:<30}: {count:>5,} patients ({percentage:>5.1f}%)")

def compare_by_patient_status(df):
    """
    Compare total_infusions distribution between Active and Discontinued patients.

    Parameters:
    -----------
    df : pd.DataFrame
        The preprocessed dataframe
    """
    print("\n" + "-" * 50)
    print("COMPARATIVE ANALYSIS: ACTIVE vs DISCONTINUED PATIENTS")
    print("-" * 50)

    # Calculate statistics by status
    stats_by_status = df.groupby('patient_status')['total_infusions'].describe().round(2)

    print("📊 DESCRIPTIVE STATISTICS BY PATIENT STATUS:")
    print(stats_by_status)

    # Calculate additional metrics
    print(f"\n📈 KEY DIFFERENCES:")
    active_mean = stats_by_status.loc['Active', 'mean']
    discontinued_mean = stats_by_status.loc['Discontinued', 'mean']
    mean_diff = discontinued_mean - active_mean

    active_median = stats_by_status.loc['Active', '50%']
    discontinued_median = stats_by_status.loc['Discontinued', '50%']
    median_diff = discontinued_median - active_median

    print(f"   • Mean difference:   {mean_diff:>+7.2f} infusions (Discontinued - Active)")
    print(f"   • Median difference: {median_diff:>+7.2f} infusions (Discontinued - Active)")

    # Statistical significance test
    active_infusions = df[df['patient_status'] == 'Active']['total_infusions']
    discontinued_infusions = df[df['patient_status'] == 'Discontinued']['total_infusions']

    # Perform Welch's t-test (unequal variances)
    t_stat, p_value = stats.ttest_ind(active_infusions, discontinued_infusions, equal_var=False)

    print(f"\n🔬 STATISTICAL SIGNIFICANCE TEST (Welch's t-test):")
    print(f"   • t-statistic:       {t_stat:>8.4f}")
    print(f"   • p-value:           {p_value:>8.6f}")
    print(f"   • Significant?:      {'Yes' if p_value < 0.05 else 'No'} (α = 0.05)")

    if p_value < 0.05:
        direction = "higher" if mean_diff > 0 else "lower"
        print(f"   • Interpretation:    Discontinued patients have significantly {direction} infusion counts")
    else:
        print(f"   • Interpretation:    No significant difference in infusion counts between groups")

    # Effect size (Cohen's d)
    pooled_std = np.sqrt(((len(active_infusions) - 1) * active_infusions.var() +
                         (len(discontinued_infusions) - 1) * discontinued_infusions.var()) /
                        (len(active_infusions) + len(discontinued_infusions) - 2))
    cohens_d = mean_diff / pooled_std

    effect_size_interpretation = (
        "negligible" if abs(cohens_d) < 0.2 else
        "small" if abs(cohens_d) < 0.5 else
        "medium" if abs(cohens_d) < 0.8 else
        "large"
    )

    print(f"   • Effect size (d):   {cohens_d:>8.4f} ({effect_size_interpretation})")

    return stats_by_status

def analyze_discontinuation_patterns(df):
    """
    Analyze discontinuation patterns across different infusion ranges.

    Parameters:
    -----------
    df : pd.DataFrame
        The preprocessed dataframe
    """
    print("\n" + "-" * 50)
    print("DISCONTINUATION PATTERNS BY INFUSION RANGES")
    print("-" * 50)

    # Define infusion ranges
    ranges = [
        (1, 3, "Early therapy (1-3 infusions)"),
        (4, 6, "Short-term therapy (4-6 infusions)"),
        (7, 12, "Medium-term therapy (7-12 infusions)"),
        (13, float('inf'), "Long-term therapy (13+ infusions)")
    ]

    print("📊 DISCONTINUATION RATES BY THERAPY DURATION:")

    for min_inf, max_inf, label in ranges:
        if max_inf == float('inf'):
            range_data = df[df['total_infusions'] >= min_inf]
        else:
            range_data = df[(df['total_infusions'] >= min_inf) & (df['total_infusions'] <= max_inf)]

        if len(range_data) > 0:
            total_patients = len(range_data)
            discontinued_patients = len(range_data[range_data['patient_status'] == 'Discontinued'])
            discontinuation_rate = (discontinued_patients / total_patients) * 100

            print(f"   • {label:<30}: {discontinuation_rate:>5.1f}% ({discontinued_patients:>3}/{total_patients:>4} patients)")
        else:
            print(f"   • {label:<30}: No patients in this range")

def create_comprehensive_visualization(df):
    """
    Create a comprehensive visualization showing the relationship between
    total_infusions and patient_status.

    Parameters:
    -----------
    df : pd.DataFrame
        The preprocessed dataframe
    """
    print("\n" + "-" * 50)
    print("CREATING COMPREHENSIVE VISUALIZATION")
    print("-" * 50)

    # Set up the plotting style
    plt.style.use('default')
    sns.set_palette("viridis")

    # Create figure with subplots
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))

    # Define colors for consistency
    colors = {'Active': '#2E8B57', 'Discontinued': '#DC143C'}  # Sea green and crimson

    # Plot 1: Box plot with violin overlay
    # First create violin plot
    violin_parts = ax1.violinplot([df[df['patient_status'] == status]['total_infusions']
                                  for status in ['Active', 'Discontinued']],
                                 positions=[0, 1], widths=0.6, showmeans=False,
                                 showmedians=False, showextrema=False)

    # Color the violin plots
    for i, (status, pc) in enumerate(zip(['Active', 'Discontinued'], violin_parts['bodies'])):
        pc.set_facecolor(colors[status])
        pc.set_alpha(0.3)

    # Overlay box plot
    box_plot = ax1.boxplot([df[df['patient_status'] == status]['total_infusions']
                           for status in ['Active', 'Discontinued']],
                          positions=[0, 1], widths=0.3, patch_artist=True,
                          boxprops=dict(alpha=0.8), medianprops=dict(color='white', linewidth=2))

    # Color the box plots
    for i, (status, patch) in enumerate(zip(['Active', 'Discontinued'], box_plot['boxes'])):
        patch.set_facecolor(colors[status])

    # Customize first plot
    ax1.set_xticks([0, 1])
    ax1.set_xticklabels(['Active', 'Discontinued'])
    ax1.set_ylabel('Total Number of Infusions', fontsize=12, fontweight='bold')
    ax1.set_xlabel('Patient Status', fontsize=12, fontweight='bold')
    ax1.set_title('Distribution of Total Infusions by Patient Status\n(Box Plot with Density Overlay)',
                 fontsize=14, fontweight='bold', pad=20)
    ax1.grid(True, alpha=0.3)

    # Add statistical annotations
    active_data = df[df['patient_status'] == 'Active']['total_infusions']
    discontinued_data = df[df['patient_status'] == 'Discontinued']['total_infusions']

    # Add mean values as text
    ax1.text(0, active_data.mean() + 0.5, f'Mean: {active_data.mean():.1f}',
             ha='center', va='bottom', fontweight='bold',
             bbox=dict(boxstyle='round,pad=0.3', facecolor=colors['Active'], alpha=0.7))
    ax1.text(1, discontinued_data.mean() + 0.5, f'Mean: {discontinued_data.mean():.1f}',
             ha='center', va='bottom', fontweight='bold',
             bbox=dict(boxstyle='round,pad=0.3', facecolor=colors['Discontinued'], alpha=0.7))

    # Plot 2: Histogram comparison
    bins = np.arange(0, df['total_infusions'].max() + 2, 1) - 0.5

    ax2.hist(active_data, bins=bins, alpha=0.6, label='Active',
             color=colors['Active'], density=True, edgecolor='black', linewidth=0.5)
    ax2.hist(discontinued_data, bins=bins, alpha=0.6, label='Discontinued',
             color=colors['Discontinued'], density=True, edgecolor='black', linewidth=0.5)

    ax2.set_xlabel('Total Number of Infusions', fontsize=12, fontweight='bold')
    ax2.set_ylabel('Density', fontsize=12, fontweight='bold')
    ax2.set_title('Distribution Comparison: Total Infusions\n(Normalized Histograms)',
                 fontsize=14, fontweight='bold', pad=20)
    ax2.legend(fontsize=11)
    ax2.grid(True, alpha=0.3)

    # Add vertical lines for means
    ax2.axvline(active_data.mean(), color=colors['Active'], linestyle='--',
               linewidth=2, alpha=0.8, label=f'Active Mean: {active_data.mean():.1f}')
    ax2.axvline(discontinued_data.mean(), color=colors['Discontinued'], linestyle='--',
               linewidth=2, alpha=0.8, label=f'Discontinued Mean: {discontinued_data.mean():.1f}')

    # Update legend to include mean lines
    ax2.legend(fontsize=10)

    # Adjust layout and save
    plt.tight_layout()
    plt.savefig('total_infusions_analysis.png', dpi=300, bbox_inches='tight')
    print("✓ Visualization saved as 'total_infusions_analysis.png'")

    # Show the plot
    plt.show()

    return fig

def generate_insights_and_recommendations(df, stats_by_status):
    """
    Generate key insights and recommendations based on the analysis.

    Parameters:
    -----------
    df : pd.DataFrame
        The preprocessed dataframe
    stats_by_status : pd.DataFrame
        Statistics grouped by patient status
    """
    print("\n" + "=" * 70)
    print("KEY INSIGHTS AND RECOMMENDATIONS")
    print("=" * 70)

    # Calculate key metrics
    active_mean = stats_by_status.loc['Active', 'mean']
    discontinued_mean = stats_by_status.loc['Discontinued', 'mean']
    mean_diff = discontinued_mean - active_mean

    total_patients = len(df)
    discontinued_patients = len(df[df['patient_status'] == 'Discontinued'])
    overall_discontinuation_rate = (discontinued_patients / total_patients) * 100

    print("🔍 KEY FINDINGS:")
    print(f"   1. Overall discontinuation rate: {overall_discontinuation_rate:.1f}%")
    print(f"   2. Average infusions - Active patients: {active_mean:.1f}")
    print(f"   3. Average infusions - Discontinued patients: {discontinued_mean:.1f}")
    print(f"   4. Difference in average infusions: {mean_diff:+.1f}")

    # Determine the primary insight
    if abs(mean_diff) < 1:
        primary_insight = "minimal difference"
        risk_assessment = "Infusion count appears to have minimal impact on discontinuation risk."
    elif mean_diff > 0:
        primary_insight = "higher infusion counts"
        risk_assessment = "Patients with more infusions may be at higher risk of discontinuation."
    else:
        primary_insight = "lower infusion counts"
        risk_assessment = "Patients with fewer infusions may be at higher risk of discontinuation."

    print(f"\n💡 PRIMARY INSIGHT:")
    print(f"   Discontinued patients show {primary_insight} compared to active patients.")
    print(f"   {risk_assessment}")

    # Risk stratification
    print(f"\n⚠️  RISK STRATIFICATION:")
    ranges = [
        (1, 3, "Early therapy (1-3 infusions)"),
        (4, 6, "Short-term therapy (4-6 infusions)"),
        (7, 12, "Medium-term therapy (7-12 infusions)"),
        (13, float('inf'), "Long-term therapy (13+ infusions)")
    ]

    highest_risk_rate = 0
    highest_risk_range = ""

    for min_inf, max_inf, label in ranges:
        if max_inf == float('inf'):
            range_data = df[df['total_infusions'] >= min_inf]
        else:
            range_data = df[(df['total_infusions'] >= min_inf) & (df['total_infusions'] <= max_inf)]

        if len(range_data) > 0:
            discontinuation_rate = (len(range_data[range_data['patient_status'] == 'Discontinued']) / len(range_data)) * 100
            if discontinuation_rate > highest_risk_rate:
                highest_risk_rate = discontinuation_rate
                highest_risk_range = label

            risk_level = "🔴 HIGH" if discontinuation_rate > overall_discontinuation_rate + 5 else "🟡 MEDIUM" if discontinuation_rate > overall_discontinuation_rate else "🟢 LOW"
            print(f"   • {label:<30}: {discontinuation_rate:>5.1f}% {risk_level}")

    print(f"\n🎯 RECOMMENDATIONS:")
    print(f"   1. Focus monitoring on {highest_risk_range.lower()} patients ({highest_risk_rate:.1f}% discontinuation rate)")
    print(f"   2. Implement targeted retention strategies for high-risk infusion ranges")
    print(f"   3. Investigate specific reasons for discontinuation in high-risk groups")
    print(f"   4. Consider proactive patient engagement programs")

    if mean_diff > 2:
        print(f"   5. ⚠️  ALERT: Patients with higher infusion counts show elevated discontinuation risk")
        print(f"      - Consider fatigue/burden factors in long-term therapy patients")
    elif mean_diff < -2:
        print(f"   5. ⚠️  ALERT: Early discontinuation pattern detected")
        print(f"      - Focus on early therapy support and education programs")

    print(f"\n📊 STATISTICAL SUMMARY:")
    print(f"   • Total patients analyzed: {total_patients:,}")
    print(f"   • Active patients: {len(df[df['patient_status'] == 'Active']):,}")
    print(f"   • Discontinued patients: {discontinued_patients:,}")
    print(f"   • Infusion range: {df['total_infusions'].min():.0f} - {df['total_infusions'].max():.0f}")

def main():
    """
    Main function to run the complete EDA analysis.
    """
    # Load and preprocess data
    df = load_and_preprocess_data()

    if df is None:
        print("❌ Analysis terminated due to data loading issues.")
        return

    # Perform analysis steps
    analyze_total_infusions_distribution(df)
    stats_by_status = compare_by_patient_status(df)
    analyze_discontinuation_patterns(df)
    create_comprehensive_visualization(df)
    generate_insights_and_recommendations(df, stats_by_status)

    print("\n" + "=" * 70)
    print("ANALYSIS COMPLETE")
    print("=" * 70)
    print("✓ Comprehensive EDA completed successfully")
    print("✓ Visualization saved as 'total_infusions_analysis.png'")
    print("✓ Ready for further analysis or presentation")

if __name__ == "__main__":
    main()
