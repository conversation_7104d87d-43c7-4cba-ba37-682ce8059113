"""
Baseline Ensemble Models for Ocrevus Discontinuation Prediction

This script builds and evaluates three baseline ensemble models:
1. Random Forest
2. Gradient Boosted Decision Trees
3. XGBoost

Author: Augment Agent
Date: 2025
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split, StratifiedKFold
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.metrics import roc_auc_score, precision_score, classification_report, confusion_matrix
from sklearn.metrics import roc_curve, precision_recall_curve
import xgboost as xgb

# SHAP for feature explainability
try:
    import shap
    SHAP_AVAILABLE = True
    print("✓ SHAP available for feature explainability analysis")
except ImportError:
    SHAP_AVAILABLE = False
    print("⚠️  SHAP not available - install with: python -m pip install shap")

import warnings
warnings.filterwarnings('ignore')

# Set random seed for reproducibility
RANDOM_STATE = 42
np.random.seed(RANDOM_STATE)

def load_and_preprocess_data(file_path='ocrevus_feat_eng_output.csv'):
    """
    Load the feature engineering output and prepare for modeling.
    
    Parameters:
    -----------
    file_path : str
        Path to the feature engineering output CSV file
        
    Returns:
    --------
    X_train, X_test, y_train, y_test : arrays
        Train-test split data
    feature_names : list
        List of feature column names
    """
    print("=" * 80)
    print("BASELINE ENSEMBLE MODELS FOR OCREVUS DISCONTINUATION PREDICTION")
    print("=" * 80)
    
    # Load the dataset
    try:
        df = pd.read_csv(file_path)
        print(f"✓ Dataset loaded successfully: {df.shape[0]:,} rows × {df.shape[1]} columns")
    except FileNotFoundError:
        print(f"❌ Error: File '{file_path}' not found.")
        return None, None, None, None, None
    except Exception as e:
        print(f"❌ Error loading data: {e}")
        return None, None, None, None, None
    
    # Data preprocessing
    print("\n" + "-" * 60)
    print("DATA PREPROCESSING")
    print("-" * 60)
    
    # Remove specified columns
    columns_to_exclude = ['patientid', 'latest_infusion_dt', 'next_estimated_infusion_date']
    print(f"Excluding columns: {columns_to_exclude}")
    
    # Check if columns exist before dropping
    existing_exclude_cols = [col for col in columns_to_exclude if col in df.columns]
    if existing_exclude_cols:
        df = df.drop(columns=existing_exclude_cols)
        print(f"✓ Removed {len(existing_exclude_cols)} columns")
    
    # Separate features and target
    target_col = 'discontinue_flag'
    if target_col not in df.columns:
        print(f"❌ Error: Target column '{target_col}' not found in dataset")
        return None, None, None, None, None
    
    X = df.drop(columns=[target_col])
    y = df[target_col]
    feature_names = X.columns.tolist()
    
    print(f"✓ Features: {X.shape[1]} columns")
    print(f"✓ Target variable: {target_col}")
    print(f"✓ Target distribution:")
    print(f"   - Class 0 (Active): {(y == 0).sum():,} ({(y == 0).mean()*100:.1f}%)")
    print(f"   - Class 1 (Discontinued): {(y == 1).sum():,} ({(y == 1).mean()*100:.1f}%)")
    
    # Check for missing values
    missing_values = X.isnull().sum().sum()
    if missing_values > 0:
        print(f"⚠️  Warning: {missing_values} missing values found")
        # Handle missing values if needed
        X = X.fillna(X.median())
        print("✓ Missing values filled with median")
    else:
        print("✓ No missing values found")
    
    # Perform stratified train-test split (70-30)
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, 
        test_size=0.3, 
        random_state=RANDOM_STATE, 
        stratify=y
    )
    
    print(f"\n✓ Train-test split completed:")
    print(f"   - Training set: {X_train.shape[0]:,} samples ({X_train.shape[0]/len(df)*100:.1f}%)")
    print(f"   - Test set: {X_test.shape[0]:,} samples ({X_test.shape[0]/len(df)*100:.1f}%)")
    print(f"   - Training target distribution: {y_train.value_counts().to_dict()}")
    print(f"   - Test target distribution: {y_test.value_counts().to_dict()}")
    
    return X_train, X_test, y_train, y_test, feature_names

def build_baseline_models():
    """
    Initialize baseline ensemble models with default parameters.
    
    Returns:
    --------
    dict
        Dictionary containing model instances
    """
    print("\n" + "-" * 60)
    print("INITIALIZING BASELINE MODELS")
    print("-" * 60)
    
    models = {
        'Random Forest': RandomForestClassifier(random_state=RANDOM_STATE),
        'Gradient Boosting': GradientBoostingClassifier(random_state=RANDOM_STATE),
        'XGBoost': xgb.XGBClassifier(random_state=RANDOM_STATE, eval_metric='logloss')
    }
    
    print("✓ Initialized 3 baseline ensemble models:")
    for name, model in models.items():
        print(f"   - {name}: {type(model).__name__}")
    
    return models

def train_and_evaluate_models(models, X_train, X_test, y_train, y_test):
    """
    Train all models and evaluate their performance.
    
    Parameters:
    -----------
    models : dict
        Dictionary of model instances
    X_train, X_test, y_train, y_test : arrays
        Train-test split data
        
    Returns:
    --------
    dict
        Dictionary containing trained models and results
    """
    print("\n" + "-" * 60)
    print("TRAINING AND EVALUATION")
    print("-" * 60)
    
    results = {}
    
    for name, model in models.items():
        print(f"\n🔄 Training {name}...")
        
        # Train the model
        model.fit(X_train, y_train)
        
        # Make predictions
        y_pred = model.predict(X_test)
        y_pred_proba = model.predict_proba(X_test)[:, 1]
        
        # Calculate metrics
        auc_roc = roc_auc_score(y_test, y_pred_proba)
        precision = precision_score(y_test, y_pred)
        
        # Store results
        results[name] = {
            'model': model,
            'y_pred': y_pred,
            'y_pred_proba': y_pred_proba,
            'auc_roc': auc_roc,
            'precision': precision
        }
        
        print(f"✓ {name} completed:")
        print(f"   - AUC-ROC: {auc_roc:.4f}")
        print(f"   - Precision: {precision:.4f}")
    
    return results

def create_performance_comparison(results, y_test):
    """
    Create comprehensive performance comparison and visualizations.
    
    Parameters:
    -----------
    results : dict
        Dictionary containing model results
    y_test : array
        True test labels
    """
    print("\n" + "=" * 60)
    print("MODEL PERFORMANCE COMPARISON")
    print("=" * 60)
    
    # Create performance summary table
    performance_data = []
    for name, result in results.items():
        performance_data.append({
            'Model': name,
            'AUC-ROC': result['auc_roc'],
            'Precision': result['precision']
        })
    
    performance_df = pd.DataFrame(performance_data)
    performance_df = performance_df.sort_values('AUC-ROC', ascending=False)
    
    print("\n📊 PERFORMANCE SUMMARY:")
    print(performance_df.to_string(index=False, float_format='%.4f'))
    
    # Find best performing model
    best_model_name = performance_df.iloc[0]['Model']
    best_auc = performance_df.iloc[0]['AUC-ROC']
    best_precision = performance_df.iloc[0]['Precision']
    
    print(f"\n🏆 BEST PERFORMING MODEL: {best_model_name}")
    print(f"   - AUC-ROC: {best_auc:.4f}")
    print(f"   - Precision: {best_precision:.4f}")
    
    # Create visualizations
    create_performance_visualizations(results, y_test, performance_df)
    
    return performance_df

def create_performance_visualizations(results, y_test, performance_df):
    """
    Create comprehensive visualizations for model performance.
    
    Parameters:
    -----------
    results : dict
        Dictionary containing model results
    y_test : array
        True test labels
    performance_df : DataFrame
        Performance summary dataframe
    """
    # Set up the plotting style
    plt.style.use('default')
    fig = plt.figure(figsize=(20, 12))
    
    # 1. ROC Curves
    plt.subplot(2, 4, 1)
    for name, result in results.items():
        fpr, tpr, _ = roc_curve(y_test, result['y_pred_proba'])
        plt.plot(fpr, tpr, label=f"{name} (AUC = {result['auc_roc']:.3f})", linewidth=2)
    
    plt.plot([0, 1], [0, 1], 'k--', alpha=0.5)
    plt.xlabel('False Positive Rate')
    plt.ylabel('True Positive Rate')
    plt.title('ROC Curves Comparison')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 2. Precision-Recall Curves
    plt.subplot(2, 4, 2)
    for name, result in results.items():
        precision_curve, recall_curve, _ = precision_recall_curve(y_test, result['y_pred_proba'])
        plt.plot(recall_curve, precision_curve, label=f"{name}", linewidth=2)
    
    plt.xlabel('Recall')
    plt.ylabel('Precision')
    plt.title('Precision-Recall Curves')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 3. AUC-ROC Comparison Bar Chart
    plt.subplot(2, 4, 3)
    bars = plt.bar(performance_df['Model'], performance_df['AUC-ROC'], 
                   color=['#1f77b4', '#ff7f0e', '#2ca02c'])
    plt.title('AUC-ROC Comparison')
    plt.ylabel('AUC-ROC Score')
    plt.xticks(rotation=45)
    plt.ylim(0, 1)
    
    # Add value labels on bars
    for bar, value in zip(bars, performance_df['AUC-ROC']):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01, 
                f'{value:.3f}', ha='center', va='bottom')
    
    # 4. Precision Comparison Bar Chart
    plt.subplot(2, 4, 4)
    bars = plt.bar(performance_df['Model'], performance_df['Precision'], 
                   color=['#1f77b4', '#ff7f0e', '#2ca02c'])
    plt.title('Precision Comparison')
    plt.ylabel('Precision Score')
    plt.xticks(rotation=45)
    plt.ylim(0, 1)
    
    # Add value labels on bars
    for bar, value in zip(bars, performance_df['Precision']):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01, 
                f'{value:.3f}', ha='center', va='bottom')
    
    # 5-7. Confusion Matrices
    for i, (name, result) in enumerate(results.items()):
        plt.subplot(2, 4, 5 + i)
        cm = confusion_matrix(y_test, result['y_pred'])
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
                   xticklabels=['Active', 'Discontinued'],
                   yticklabels=['Active', 'Discontinued'])
        plt.title(f'{name}\nConfusion Matrix')
        plt.ylabel('True Label')
        plt.xlabel('Predicted Label')
    
    # 8. Performance Summary Table
    plt.subplot(2, 4, 8)
    plt.axis('off')
    
    # Create table text
    table_text = "PERFORMANCE SUMMARY\n" + "="*25 + "\n\n"
    for _, row in performance_df.iterrows():
        table_text += f"{row['Model']}:\n"
        table_text += f"  AUC-ROC: {row['AUC-ROC']:.4f}\n"
        table_text += f"  Precision: {row['Precision']:.4f}\n\n"
    
    plt.text(0.1, 0.9, table_text, transform=plt.gca().transAxes, 
             fontsize=10, verticalalignment='top', fontfamily='monospace')
    
    plt.tight_layout()
    plt.savefig('baseline_ensemble_models_evaluation.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("\n✅ Comprehensive visualizations created and saved as 'baseline_ensemble_models_evaluation.png'")

def analyze_feature_importance_with_shap(results, X_test, y_test, feature_names, use_full_dataset=True):
    """
    Perform SHAP analysis for feature explainability across all models.

    Parameters:
    -----------
    results : dict
        Dictionary containing trained models and results
    X_test : DataFrame or array
        Test features
    y_test : array
        Test labels
    feature_names : list
        List of feature names
    use_full_dataset : bool
        Whether to use the entire test dataset for SHAP analysis (default: True)

    Returns:
    --------
    dict
        Dictionary containing SHAP values and explainers for each model
    """
    if not SHAP_AVAILABLE:
        print("❌ SHAP not available - cannot perform feature explainability analysis")
        print("📋 Install SHAP with: python -m pip install shap")
        return None

    print("\n" + "=" * 80)
    print("FEATURE EXPLAINABILITY ANALYSIS WITH SHAP")
    print("=" * 80)

    # Use full test dataset for comprehensive SHAP analysis
    if use_full_dataset:
        X_sample = X_test
        y_sample = y_test
        print(f"📊 Using entire test dataset ({len(X_test):,} instances) for comprehensive SHAP analysis")
    else:
        # Fallback to sampling if needed for computational constraints
        sample_size = 500
        if len(X_test) > sample_size:
            sample_indices = np.random.choice(len(X_test), sample_size, replace=False)
            X_sample = X_test.iloc[sample_indices] if hasattr(X_test, 'iloc') else X_test[sample_indices]
            y_sample = y_test.iloc[sample_indices] if hasattr(y_test, 'iloc') else y_test[sample_indices]
            print(f"📊 Using random sample of {sample_size} instances for SHAP analysis")
        else:
            X_sample = X_test
            y_sample = y_test
            print(f"📊 Using all {len(X_test)} test instances for SHAP analysis")

    shap_results = {}

    for name, result in results.items():
        print(f"\n🔄 Computing SHAP values for {name}...")

        try:
            model = result['model']

            # Create TreeExplainer for tree-based models
            explainer = shap.TreeExplainer(model)
            shap_values = explainer.shap_values(X_sample)

            # For binary classification, get SHAP values for positive class
            if isinstance(shap_values, list) and len(shap_values) == 2:
                shap_values = shap_values[1]  # Positive class (discontinued)
            elif isinstance(shap_values, np.ndarray) and shap_values.ndim == 3:
                shap_values = shap_values[:, :, 1]  # Positive class (discontinued)
            elif isinstance(shap_values, np.ndarray) and shap_values.shape[-1] == 2:
                shap_values = shap_values[:, 1]  # Positive class (discontinued)

            shap_results[name] = {
                'explainer': explainer,
                'shap_values': shap_values,
                'X_sample': X_sample,
                'y_sample': y_sample
            }

            print(f"✓ {name} SHAP analysis completed")

        except Exception as e:
            print(f"❌ Error computing SHAP values for {name}: {e}")
            continue

    if shap_results:
        # Create SHAP visualizations
        create_shap_visualizations(shap_results, feature_names)

        # Generate feature importance summary
        generate_feature_importance_summary(shap_results, feature_names)

    return shap_results

def create_shap_visualizations(shap_results, feature_names):
    """
    Create comprehensive SHAP visualizations for all models.

    Parameters:
    -----------
    shap_results : dict
        Dictionary containing SHAP values and explainers
    feature_names : list
        List of feature names
    """
    print(f"\n📊 Creating SHAP visualizations...")

    # 1. Feature Importance Plots (Mean Absolute SHAP Values)
    fig, axes = plt.subplots(1, len(shap_results), figsize=(6*len(shap_results), 8))
    if len(shap_results) == 1:
        axes = [axes]

    for i, (name, shap_data) in enumerate(shap_results.items()):
        shap_values = shap_data['shap_values']
        X_sample = shap_data['X_sample']

        # Calculate mean absolute SHAP values
        mean_shap = np.abs(shap_values).mean(axis=0)

        # Ensure mean_shap is 1-dimensional
        if mean_shap.ndim > 1:
            mean_shap = mean_shap.flatten()

        # Ensure we have the right number of features
        if len(mean_shap) != len(feature_names):
            print(f"⚠️  Warning: SHAP values length ({len(mean_shap)}) doesn't match features ({len(feature_names)})")
            min_len = min(len(mean_shap), len(feature_names))
            mean_shap = mean_shap[:min_len]
            current_features = feature_names[:min_len]
        else:
            current_features = feature_names

        feature_importance = pd.DataFrame({
            'feature': current_features,
            'importance': mean_shap
        }).sort_values('importance', ascending=True)

        # Plot top 15 features
        top_features = feature_importance.tail(15)
        axes[i].barh(range(len(top_features)), top_features['importance'])
        axes[i].set_yticks(range(len(top_features)))
        axes[i].set_yticklabels(top_features['feature'])
        axes[i].set_xlabel('Mean |SHAP Value|')
        axes[i].set_title(f'{name}\nFeature Importance')
        axes[i].grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig('shap_feature_importance_comparison.png', dpi=300, bbox_inches='tight')
    plt.show()

    # 2. SHAP Summary Plots for each model
    for name, shap_data in shap_results.items():
        try:
            plt.figure(figsize=(10, 8))
            shap.summary_plot(shap_data['shap_values'], shap_data['X_sample'],
                             feature_names=feature_names, show=False, max_display=15)
            plt.title(f'{name} - SHAP Summary Plot')
            plt.tight_layout()
            plt.savefig(f'shap_summary_{name.lower().replace(" ", "_")}.png', dpi=300, bbox_inches='tight')
            plt.show()
        except Exception as e:
            print(f"⚠️  Could not create summary plot for {name}: {e}")
            continue

    # 3. Waterfall plots for representative cases (XGBoost only for simplicity)
    if 'XGBoost' in shap_results:
        try:
            shap_data = shap_results['XGBoost']
            shap_values = shap_data['shap_values']
            X_sample = shap_data['X_sample']

            # Create waterfall plots for 3 representative cases
            sample_indices = [0, len(shap_values)//2, -1]  # First, middle, last

            for i, idx in enumerate(sample_indices):
                if idx >= len(shap_values):
                    continue

                plt.figure(figsize=(12, 8))
                try:
                    # Create explanation object
                    explanation = shap.Explanation(
                        values=shap_values[idx],
                        base_values=shap_data['explainer'].expected_value,
                        data=X_sample.iloc[idx] if hasattr(X_sample, 'iloc') else X_sample[idx],
                        feature_names=feature_names
                    )
                    shap.waterfall_plot(explanation, show=False, max_display=10)
                    plt.title(f'XGBoost - Waterfall Plot (Sample {i+1})')
                    plt.tight_layout()
                    plt.savefig(f'shap_waterfall_sample_{i+1}.png', dpi=300, bbox_inches='tight')
                    plt.show()
                except Exception as e:
                    print(f"⚠️  Could not create waterfall plot for sample {i+1}: {e}")
                    plt.close()
                    continue
        except Exception as e:
            print(f"⚠️  Could not create waterfall plots: {e}")

    print("✅ SHAP visualizations created and saved")

def generate_feature_importance_summary(shap_results, feature_names):
    """
    Generate a comprehensive summary of feature importance across models.

    Parameters:
    -----------
    shap_results : dict
        Dictionary containing SHAP values and explainers
    feature_names : list
        List of feature names
    """
    print(f"\n📋 FEATURE IMPORTANCE SUMMARY:")
    print("=" * 60)

    # Calculate feature importance for each model
    importance_data = {}

    for name, shap_data in shap_results.items():
        shap_values = shap_data['shap_values']
        mean_shap = np.abs(shap_values).mean(axis=0)

        # Ensure mean_shap is 1-dimensional
        if mean_shap.ndim > 1:
            mean_shap = mean_shap.flatten()

        # Ensure we have the right number of features
        if len(mean_shap) != len(feature_names):
            min_len = min(len(mean_shap), len(feature_names))
            mean_shap = mean_shap[:min_len]

        importance_data[name] = mean_shap

    # Create combined importance DataFrame
    # Find the minimum length to ensure all arrays are the same size
    min_length = min(len(feature_names), min(len(v) for v in importance_data.values()))

    # Truncate all arrays to the minimum length
    truncated_data = {}
    for name, values in importance_data.items():
        truncated_data[name] = values[:min_length]

    truncated_features = feature_names[:min_length]

    importance_df = pd.DataFrame(truncated_data, index=truncated_features)
    importance_df['Mean_Across_Models'] = importance_df.mean(axis=1)
    importance_df = importance_df.sort_values('Mean_Across_Models', ascending=False)

    print("\n🏆 TOP 10 MOST IMPORTANT FEATURES (Mean |SHAP Value|):")
    print("-" * 60)
    top_10 = importance_df.head(10)
    for i, (feature, row) in enumerate(top_10.iterrows(), 1):
        print(f"{i:2d}. {feature:<30} {row['Mean_Across_Models']:.4f}")
        for model_name in shap_results.keys():
            print(f"    {model_name:<20}: {row[model_name]:.4f}")
        print()

    # Save detailed importance to CSV
    importance_df.to_csv('shap_feature_importance_detailed.csv')
    print("✅ Detailed feature importance saved to 'shap_feature_importance_detailed.csv'")

    return importance_df

def main():
    """
    Main function to run the complete baseline ensemble modeling pipeline.
    """
    # Load and preprocess data
    X_train, X_test, y_train, y_test, feature_names = load_and_preprocess_data()
    
    if X_train is None:
        print("❌ Pipeline terminated due to data loading issues.")
        return
    
    # Initialize models
    models = build_baseline_models()
    
    # Train and evaluate models
    results = train_and_evaluate_models(models, X_train, X_test, y_train, y_test)
    
    # Create performance comparison
    performance_df = create_performance_comparison(results, y_test)

    # Perform SHAP analysis for feature explainability
    shap_results = analyze_feature_importance_with_shap(results, X_test, y_test, feature_names)

    # Generate final summary
    print("\n" + "=" * 80)
    print("BASELINE ENSEMBLE MODELING COMPLETE")
    print("=" * 80)
    print("✅ Successfully trained and evaluated 3 baseline ensemble models")
    print("✅ Performance comparison completed")
    print("✅ Visualizations saved as 'baseline_ensemble_models_evaluation.png'")
    if shap_results:
        print("✅ SHAP feature explainability analysis completed")
        print("✅ SHAP visualizations and importance rankings saved")
    print("\n📋 NEXT STEPS:")
    print("   1. Review model performance metrics and SHAP feature importance")
    print("   2. Consider hyperparameter tuning for best performing model")
    print("   3. Analyze clinical significance of top predictive features")
    print("   4. Develop targeted intervention strategies based on feature insights")

    return results, performance_df, shap_results

if __name__ == "__main__":
    results, performance_df, shap_results = main()
