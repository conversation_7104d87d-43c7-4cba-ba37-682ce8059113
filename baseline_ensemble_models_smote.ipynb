{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Baseline Ensemble Models for Ocrevus Discontinuation Prediction\n", "\n", "## Project Overview\n", "\n", "This notebook presents a comprehensive analysis of baseline ensemble models for predicting Ocrevus therapy discontinuation, including advanced feature explainability analysis using SHAP (SHapley Additive exPlanations). We evaluate three state-of-the-art ensemble algorithms to establish baseline performance metrics and identify the most effective approach for patient risk assessment.\n", "\n", "### Objectives\n", "- Build and evaluate three baseline ensemble models: Random Forest, Gradient Boosting, and XGBoost\n", "- Compare model performance using AUC-ROC and Precision metrics\n", "- **Perform comprehensive SHAP analysis for feature explainability using the entire test dataset**\n", "- **Identify key patient characteristics that drive discontinuation risk**\n", "- Provide recommendations for production deployment\n", "- Establish foundation for future model enhancement\n", "\n", "### Dataset Description\n", "- **Source**: `ocrevus_feat_eng_output.csv` - Feature engineering output from Ocrevus patient data\n", "- **Target Variable**: `discontinue_flag` (binary: 0=Active, 1=Discontinued)\n", "- **Features**: 25 engineered features after excluding non-predictive columns\n", "- **<PERSON><PERSON> Size**: 12,697 patients\n", "\n", "### Modeling Approach\n", "- **Data Split**: 70-30 stratified train-test split\n", "- **Model Configuration**: Default parameters for baseline performance\n", "- **Evaluation Metrics**: AUC-ROC (discrimination) and Precision (false positive minimization)\n", "- **Explainability**: SHAP analysis using the complete test dataset (3,810 instances) for maximum accuracy\n", "\n", "---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Environment Setup and Package Imports\n", "\n", "First, we'll import all necessary packages including SHAP for comprehensive feature explainability analysis."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Core data manipulation and analysis\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "# Optional packages with fallback handling\n", "try:\n", "    import seaborn as sns\n", "    SEABORN_AVAILABLE = True\n", "    sns.set_style(\"whitegrid\")\n", "except ImportError:\n", "    SEABORN_AVAILABLE = False\n", "    print(\"⚠️  Seaborn not available - using matplotlib only\")\n", "\n", "# Machine learning packages\n", "try:\n", "    from sklearn.model_selection import train_test_split\n", "    from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier\n", "    from sklearn.metrics import roc_auc_score, precision_score, classification_report, confusion_matrix\n", "    from sklearn.metrics import roc_curve, precision_recall_curve\n", "    SKLEARN_AVAILABLE = True\n", "    print(\"✓ Scikit-learn available\")\n", "except ImportError:\n", "    SKLEARN_AVAILABLE = False\n", "    print(\"❌ Scikit-learn not available\")\n", "\n", "try:\n", "    import xgboost as xgb\n", "    XGBOOST_AVAILABLE = True\n", "    print(\"✓ XGBoost available\")\n", "except ImportError:\n", "    XGBOOST_AVAILABLE = False\n", "    print(\"❌ XGBoost not available\")\n", "\n", "# SHAP for feature explainability\n", "try:\n", "    import shap\n", "    SHAP_AVAILABLE = True\n", "    print(\"✓ SHAP available for feature explainability analysis\")\n", "except ImportError:\n", "    SHAP_AVAILABLE = False\n", "    print(\"❌ SHAP not available - install with: python -m pip install shap\")\n", "    print(\"   Run this command and restart the notebook: !pip install shap\")\n", "\n", "# Configuration\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set random seed for reproducibility\n", "RANDOM_STATE = 42\n", "np.random.seed(RANDOM_STATE)\n", "\n", "# Display settings\n", "pd.set_option('display.max_columns', None)\n", "pd.set_option('display.width', None)\n", "plt.rcParams['figure.figsize'] = (12, 8)\n", "\n", "print(\"\\n\" + \"=\"*60)\n", "print(\"ENVIRONMENT SETUP COMPLETE\")\n", "print(\"=\"*60)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Data Loading and Initial Exploration\n", "\n", "We'll load the feature engineering output and examine the dataset structure, target distribution, and data quality."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load the dataset\n", "print(\"=\"*80)\n", "print(\"DATA LOADING AND EXPLORATION\")\n", "print(\"=\"*80)\n", "\n", "try:\n", "    df = pd.read_csv('ocrevus_feat_eng_output.csv')\n", "    print(f\"✓ Dataset loaded successfully: {df.shape[0]:,} rows × {df.shape[1]} columns\")\n", "except FileNotFoundError:\n", "    print(\"❌ Error: File 'ocrevus_feat_eng_output.csv' not found.\")\n", "    raise\n", "except Exception as e:\n", "    print(f\"❌ Error loading data: {e}\")\n", "    raise\n", "\n", "# Display basic information about the dataset\n", "print(f\"\\n📊 DATASET OVERVIEW:\")\n", "print(f\"   • Shape: {df.shape}\")\n", "print(f\"   • Memory usage: {df.memory_usage(deep=True).sum() / 1024**2:.2f} MB\")\n", "print(f\"   • Data types: {df.dtypes.value_counts().to_dict()}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Display column information\n", "print(\"\\n📋 COLUMN INFORMATION:\")\n", "print(f\"Total columns: {len(df.columns)}\")\n", "print(\"\\nColumn names:\")\n", "for i, col in enumerate(df.columns, 1):\n", "    print(f\"{i:2d}. {col}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Display first few rows\n", "print(\"\\n🔍 FIRST 5 ROWS:\")\n", "display(df.head())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Check for missing values and target variable\n", "print(\"\\n🔍 DATA QUALITY CHECK:\")\n", "missing_values = df.isnull().sum()\n", "total_missing = missing_values.sum()\n", "\n", "if total_missing > 0:\n", "    print(f\"⚠️  Warning: {total_missing} missing values found\")\n", "    print(\"\\nMissing values by column:\")\n", "    missing_cols = missing_values[missing_values > 0]\n", "    for col, count in missing_cols.items():\n", "        print(f\"   • {col}: {count} ({count/len(df)*100:.2f}%)\")\n", "else:\n", "    print(\"✓ No missing values found\")\n", "\n", "# Check target variable\n", "target_col = 'discontinue_flag'\n", "if target_col in df.columns:\n", "    print(f\"\\n🎯 TARGET VARIABLE ANALYSIS ({target_col}):\")\n", "    target_dist = df[target_col].value_counts().sort_index()\n", "    target_pct = df[target_col].value_counts(normalize=True).sort_index() * 100\n", "    \n", "    print(\"Distribution:\")\n", "    for value, count in target_dist.items():\n", "        label = \"Active\" if value == 0 else \"Discontinued\"\n", "        print(f\"   • Class {value} ({label}): {count:,} ({target_pct[value]:.1f}%)\")\n", "else:\n", "    print(f\"❌ Error: Target column '{target_col}' not found in dataset\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Data Preprocessing\n", "\n", "We'll prepare the data for modeling by removing non-predictive columns and creating the train-test split."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"\\n\" + \"-\"*60)\n", "print(\"DATA PREPROCESSING\")\n", "print(\"-\"*60)\n", "\n", "# Remove specified columns\n", "columns_to_exclude = ['patientid', 'latest_infusion_dt', 'next_estimated_infusion_date']\n", "print(f\"Excluding columns: {columns_to_exclude}\")\n", "\n", "# Check if columns exist before dropping\n", "existing_exclude_cols = [col for col in columns_to_exclude if col in df.columns]\n", "if existing_exclude_cols:\n", "    df_processed = df.drop(columns=existing_exclude_cols)\n", "    print(f\"✓ Removed {len(existing_exclude_cols)} columns: {existing_exclude_cols}\")\n", "else:\n", "    df_processed = df.copy()\n", "    print(\"⚠️  No specified columns found to remove\")\n", "\n", "# Separate features and target\n", "target_col = 'discontinue_flag'\n", "if target_col not in df_processed.columns:\n", "    raise ValueError(f\"Target column '{target_col}' not found in dataset\")\n", "\n", "X = df_processed.drop(columns=[target_col])\n", "y = df_processed[target_col]\n", "feature_names = X.columns.tolist()\n", "\n", "print(f\"\\n✓ Features: {X.shape[1]} columns\")\n", "print(f\"✓ Target variable: {target_col}\")\n", "print(f\"✓ Final dataset shape: {X.shape}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Handle missing values if any\n", "missing_values = X.isnull().sum().sum()\n", "if missing_values > 0:\n", "    print(f\"⚠️  Warning: {missing_values} missing values found in features\")\n", "    X = <PERSON>.fillna(X.median())\n", "    print(\"✓ Missing values filled with median\")\n", "else:\n", "    print(\"✓ No missing values found in features\")\n", "\n", "# Display feature information\n", "print(f\"\\n📋 FEATURE SUMMARY:\")\n", "print(f\"   • Total features: {len(feature_names)}\")\n", "print(f\"   • Numeric features: {X.select_dtypes(include=[np.number]).shape[1]}\")\n", "print(f\"   • Non-numeric features: {X.select_dtypes(exclude=[np.number]).shape[1]}\")\n", "\n", "print(\"\\nFeature list:\")\n", "for i, feature in enumerate(feature_names, 1):\n", "    print(f\"{i:2d}. {feature}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Perform stratified train-test split (70-30)\n", "if SKLEARN_AVAILABLE:\n", "    X_train, X_test, y_train, y_test = train_test_split(\n", "        X, y, \n", "        test_size=0.3, \n", "        random_state=RANDOM_STATE, \n", "        stratify=y\n", "    )\n", "    \n", "    print(f\"\\n✓ TRAIN-TEST SPLIT COMPLETED:\")\n", "    print(f\"   • Training set: {X_train.shape[0]:,} samples ({X_train.shape[0]/len(df_processed)*100:.1f}%)\")\n", "    print(f\"   • Test set: {X_test.shape[0]:,} samples ({X_test.shape[0]/len(df_processed)*100:.1f}%)\")\n", "    \n", "    # Verify stratification worked\n", "    train_dist = y_train.value_counts().sort_index()\n", "    test_dist = y_test.value_counts().sort_index()\n", "    \n", "    print(f\"\\n📊 CLASS DISTRIBUTION VERIFICATION:\")\n", "    print(f\"   Training set: {dict(train_dist)}\")\n", "    print(f\"   Test set: {dict(test_dist)}\")\n", "    \n", "    train_pct = y_train.value_counts(normalize=True).sort_index() * 100\n", "    test_pct = y_test.value_counts(normalize=True).sort_index() * 100\n", "    \n", "    print(f\"\\n   Training percentages: {dict(train_pct.round(1))}\")\n", "    print(f\"   Test percentages: {dict(test_pct.round(1))}\")\n", "    \n", "else:\n", "    print(\"❌ Scikit-learn not available - cannot perform train-test split\")\n", "    X_train = X_test = y_train = y_test = None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Model Development\n", "\n", "We'll initialize and train three baseline ensemble models with default parameters to establish baseline performance."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"\\n\" + \"-\"*60)\n", "print(\"MODEL INITIALIZATION\")\n", "print(\"-\"*60)\n", "\n", "if not SKLEARN_AVAILABLE:\n", "    print(\"❌ Scikit-learn not available - cannot initialize models\")\n", "    print(\"📋 REQUIRED PACKAGES:\")\n", "    print(\"   pip install scikit-learn xgboost matplotlib seaborn\")\n", "    models = None\n", "else:\n", "    models = {}\n", "    \n", "    # Random Forest\n", "    models['Random Forest'] = RandomForestClassifier(random_state=RANDOM_STATE)\n", "    print(\"✓ Random Forest initialized\")\n", "    \n", "    # Gradient Boosting\n", "    models['Gradient Boosting'] = GradientBoostingClassifier(random_state=RANDOM_STATE)\n", "    print(\"✓ Gradient Boosting initialized\")\n", "    \n", "    # XGBoost (if available)\n", "    if XGBOOST_AVAILABLE:\n", "        models['XGBoost'] = xgb.XGBClassifier(random_state=RANDOM_STATE, eval_metric='logloss')\n", "        print(\"✓ XGBoost initialized\")\n", "    else:\n", "        print(\"⚠️  XGBoost not available - using only scikit-learn models\")\n", "    \n", "    print(f\"\\n📋 INITIALIZED {len(models)} BASELINE ENSEMBLE MODELS:\")\n", "    for name, model in models.items():\n", "        print(f\"   • {name}: {type(model).__name__}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Train models and collect results\n", "if models is not None and <PERSON>_<PERSON> is not None:\n", "    print(\"\\n\" + \"-\"*60)\n", "    print(\"MODEL TRAINING AND EVALUATION\")\n", "    print(\"-\"*60)\n", "    \n", "    results = {}\n", "    \n", "    for name, model in models.items():\n", "        print(f\"\\n🔄 Training {name}...\")\n", "        \n", "        # Train the model\n", "        model.fit(X_train, y_train)\n", "        \n", "        # Make predictions\n", "        y_pred = model.predict(X_test)\n", "        y_pred_proba = model.predict_proba(X_test)[:, 1]\n", "        \n", "        # Calculate metrics\n", "        auc_roc = roc_auc_score(y_test, y_pred_proba)\n", "        precision = precision_score(y_test, y_pred)\n", "        \n", "        # Store results\n", "        results[name] = {\n", "            'model': model,\n", "            'y_pred': y_pred,\n", "            'y_pred_proba': y_pred_proba,\n", "            'auc_roc': auc_roc,\n", "            'precision': precision\n", "        }\n", "        \n", "        print(f\"✓ {name} completed:\")\n", "        print(f\"   • AUC-ROC: {auc_roc:.4f}\")\n", "        print(f\"   • Precision: {precision:.4f}\")\n", "    \n", "    print(f\"\\n✅ All {len(models)} models trained successfully!\")\n", "    \n", "else:\n", "    print(\"❌ Cannot train models - missing dependencies or data\")\n", "    results = None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Model Performance Comparison\n", "\n", "Let's create a comprehensive comparison of model performance and identify the best performing baseline model."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if results is not None:\n", "    print(\"\\n\" + \"=\"*60)\n", "    print(\"MODEL PERFORMANCE COMPARISON\")\n", "    print(\"=\"*60)\n", "    \n", "    # Create performance summary table\n", "    performance_data = []\n", "    for name, result in results.items():\n", "        performance_data.append({\n", "            'Model': name,\n", "            'AUC-ROC': result['auc_roc'],\n", "            'Precision': result['precision']\n", "        })\n", "    \n", "    performance_df = pd.DataFrame(performance_data)\n", "    performance_df = performance_df.sort_values('AUC-ROC', ascending=False)\n", "    \n", "    print(\"\\n📊 PERFORMANCE SUMMARY:\")\n", "    display(performance_df.style.format({'AUC-ROC': '{:.4f}', 'Precision': '{:.4f}'}))\n", "    \n", "    # Find best performing model\n", "    best_model_name = performance_df.iloc[0]['Model']\n", "    best_auc = performance_df.iloc[0]['AUC-ROC']\n", "    best_precision = performance_df.iloc[0]['Precision']\n", "    \n", "    print(f\"\\n🏆 BEST PERFORMING MODEL: {best_model_name}\")\n", "    print(f\"   • AUC-ROC: {best_auc:.4f}\")\n", "    print(f\"   • Precision: {best_precision:.4f}\")\n", "    \n", "else:\n", "    print(\"❌ No results available for comparison\")\n", "    performance_df = None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Comprehensive Visualizations\n", "\n", "We'll create detailed visualizations to compare model performance across multiple dimensions."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if results is not None:\n", "    # Set up the plotting style\n", "    plt.style.use('default')\n", "    fig = plt.figure(figsize=(20, 12))\n", "    \n", "    # 1. <PERSON><PERSON> Cur<PERSON>\n", "    plt.subplot(2, 4, 1)\n", "    for name, result in results.items():\n", "        fpr, tpr, _ = roc_curve(y_test, result['y_pred_proba'])\n", "        plt.plot(fpr, tpr, label=f\"{name} (AUC = {result['auc_roc']:.3f})\", linewidth=2)\n", "    \n", "    plt.plot([0, 1], [0, 1], 'k--', alpha=0.5)\n", "    plt.xlabel('False Positive Rate')\n", "    plt.ylabel('True Positive Rate')\n", "    plt.title('ROC Curves Comparison')\n", "    plt.legend()\n", "    plt.grid(True, alpha=0.3)\n", "    \n", "    # 2. Precision-<PERSON><PERSON><PERSON>\n", "    plt.subplot(2, 4, 2)\n", "    for name, result in results.items():\n", "        precision_curve, recall_curve, _ = precision_recall_curve(y_test, result['y_pred_proba'])\n", "        plt.plot(recall_curve, precision_curve, label=f\"{name}\", linewidth=2)\n", "    \n", "    plt.xlabel('Recall')\n", "    plt.ylabel('Precision')\n", "    plt.title('Precision-<PERSON><PERSON><PERSON>urves')\n", "    plt.legend()\n", "    plt.grid(True, alpha=0.3)\n", "    \n", "    # 3. AUC-ROC Comparison Bar Chart\n", "    plt.subplot(2, 4, 3)\n", "    bars = plt.bar(performance_df['Model'], performance_df['AUC-ROC'], \n", "                   color=['#1f77b4', '#ff7f0e', '#2ca02c'][:len(performance_df)])\n", "    plt.title('AUC-ROC Comparison')\n", "    plt.ylabel('AUC-ROC Score')\n", "    plt.xticks(rotation=45)\n", "    plt.ylim(0, 1)\n", "    \n", "    # Add value labels on bars\n", "    for bar, value in zip(bars, performance_df['AUC-ROC']):\n", "        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01, \n", "                f'{value:.3f}', ha='center', va='bottom')\n", "    \n", "    # 4. Precision Comparison Bar Chart\n", "    plt.subplot(2, 4, 4)\n", "    bars = plt.bar(performance_df['Model'], performance_df['Precision'], \n", "                   color=['#1f77b4', '#ff7f0e', '#2ca02c'][:len(performance_df)])\n", "    plt.title('Precision Comparison')\n", "    plt.ylabel('Precision Score')\n", "    plt.xticks(rotation=45)\n", "    plt.ylim(0, 1)\n", "    \n", "    # Add value labels on bars\n", "    for bar, value in zip(bars, performance_df['Precision']):\n", "        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01, \n", "                f'{value:.3f}', ha='center', va='bottom')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    print(\"✅ Performance comparison visualizations created\")\n", "else:\n", "    print(\"❌ No results available for visualization\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Feature Explainability with SHAP\n", "\n", "SHAP (SHapley Additive exPlanations) provides a unified framework for interpreting model predictions by quantifying the contribution of each feature to individual predictions. This analysis helps us understand which patient characteristics most strongly influence discontinuation risk.\n", "\n", "### SHAP Methodology\n", "- **TreeExplainer**: Optimized for tree-based models (Random Forest, Gradient Boosting, XGBoost)\n", "- **Feature Attribution**: Each feature gets a SHAP value indicating its contribution to the prediction\n", "- **Additive Property**: SHAP values sum to the difference between prediction and expected value\n", "- **Comprehensive Analysis**: Using the entire test dataset (3,810 instances) for maximum accuracy\n", "\n", "### Why SHAP Analysis Matters\n", "- **Clinical Interpretability**: Understand which factors drive discontinuation risk\n", "- **Actionable Insights**: Identify specific patient characteristics for targeted interventions\n", "- **Model Transparency**: Explain individual predictions to healthcare providers\n", "- **Evidence-Based Care**: Support clinical decision-making with quantified feature importance"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### SHAP Analysis Implementation\n", "\n", "We'll perform comprehensive SHAP analysis using the entire test dataset to ensure maximum accuracy and reliability of our feature importance rankings."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def analyze_feature_importance_with_shap(results, X_test, y_test, feature_names, use_full_dataset=True):\n", "    \"\"\"\n", "    Perform SHAP analysis for feature explainability across all models.\n", "    \n", "    Parameters:\n", "    -----------\n", "    results : dict\n", "        Dictionary containing trained models and results\n", "    X_test : DataFrame or array\n", "        Test features\n", "    y_test : array\n", "        Test labels\n", "    feature_names : list\n", "        List of feature names\n", "    use_full_dataset : bool\n", "        Whether to use the entire test dataset for SHAP analysis (default: True)\n", "        \n", "    Returns:\n", "    --------\n", "    dict\n", "        Dictionary containing SHAP values and explainers for each model\n", "    \"\"\"\n", "    if not SHAP_AVAILABLE:\n", "        print(\"❌ SHAP not available - cannot perform feature explainability analysis\")\n", "        print(\"📋 Install SHAP with: python -m pip install shap\")\n", "        return None\n", "    \n", "    print(\"\\n\" + \"=\" * 80)\n", "    print(\"FEATURE EXPLAINABILITY ANALYSIS WITH SHAP\")\n", "    print(\"=\" * 80)\n", "    \n", "    # Use full test dataset for comprehensive SHAP analysis\n", "    if use_full_dataset:\n", "        X_sample = X_test\n", "        y_sample = y_test\n", "        print(f\"📊 Using entire test dataset ({len(X_test):,} instances) for comprehensive SHAP analysis\")\n", "    else:\n", "        # Fallback to sampling if needed for computational constraints\n", "        sample_size = 500\n", "        if len(X_test) > sample_size:\n", "            sample_indices = np.random.choice(len(X_test), sample_size, replace=False)\n", "            X_sample = X_test.iloc[sample_indices] if hasattr(X_test, 'iloc') else X_test[sample_indices]\n", "            y_sample = y_test.iloc[sample_indices] if hasattr(y_test, 'iloc') else y_test[sample_indices]\n", "            print(f\"📊 Using random sample of {sample_size} instances for SHAP analysis\")\n", "        else:\n", "            X_sample = X_test\n", "            y_sample = y_test\n", "            print(f\"📊 Using all {len(X_test)} test instances for SHAP analysis\")\n", "    \n", "    shap_results = {}\n", "    \n", "    for name, result in results.items():\n", "        print(f\"\\n🔄 Computing SHAP values for {name}...\")\n", "        \n", "        try:\n", "            model = result['model']\n", "            \n", "            # Create TreeExplainer for tree-based models\n", "            explainer = shap.<PERSON>Explainer(model)\n", "            shap_values = explainer.shap_values(X_sample)\n", "            \n", "            # For binary classification, get SHAP values for positive class\n", "            if isinstance(shap_values, list) and len(shap_values) == 2:\n", "                shap_values = shap_values[1]  # Positive class (discontinued)\n", "            elif isinstance(shap_values, np.ndarray) and shap_values.ndim == 3:\n", "                shap_values = shap_values[:, :, 1]  # Positive class (discontinued)\n", "            elif isinstance(shap_values, np.ndarray) and shap_values.shape[-1] == 2:\n", "                shap_values = shap_values[:, 1]  # Positive class (discontinued)\n", "            \n", "            shap_results[name] = {\n", "                'explainer': explainer,\n", "                'shap_values': shap_values,\n", "                'X_sample': X_sample,\n", "                'y_sample': y_sample\n", "            }\n", "            \n", "            print(f\"✓ {name} SHAP analysis completed\")\n", "            \n", "        except Exception as e:\n", "            print(f\"❌ Error computing SHAP values for {name}: {e}\")\n", "            continue\n", "    \n", "    return shap_results\n", "\n", "# Perform SHAP analysis if models and SHAP are available\n", "if results is not None and SHAP_AVAILABLE:\n", "    shap_results = analyze_feature_importance_with_shap(results, X_test, y_test, feature_names)\n", "    \n", "    if shap_results:\n", "        print(f\"\\n✅ SHAP analysis completed for {len(shap_results)} models\")\n", "    else:\n", "        print(\"❌ SHAP analysis failed\")\n", "else:\n", "    print(\"❌ Cannot perform SHAP analysis - models or SHAP not available\")\n", "    shap_results = None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### SHAP Feature Importance Visualization\n", "\n", "The following visualizations show feature importance across all three models using mean absolute SHAP values."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create SHAP feature importance comparison\n", "if shap_results is not None:\n", "    # Feature Importance Plots (Mean Absolute SHAP Values)\n", "    fig, axes = plt.subplots(1, len(shap_results), figsize=(6*len(shap_results), 8))\n", "    if len(shap_results) == 1:\n", "        axes = [axes]\n", "    \n", "    for i, (name, shap_data) in enumerate(shap_results.items()):\n", "        shap_values = shap_data['shap_values']\n", "        \n", "        # Calculate mean absolute SHAP values\n", "        mean_shap = np.abs(shap_values).mean(axis=0)\n", "        \n", "        # Ensure mean_shap is 1-dimensional\n", "        if mean_shap.ndim > 1:\n", "            mean_shap = mean_shap.flatten()\n", "        \n", "        # Ensure we have the right number of features\n", "        if len(mean_shap) != len(feature_names):\n", "            min_len = min(len(mean_shap), len(feature_names))\n", "            mean_shap = mean_shap[:min_len]\n", "            current_features = feature_names[:min_len]\n", "        else:\n", "            current_features = feature_names\n", "        \n", "        feature_importance = pd.DataFrame({\n", "            'feature': current_features,\n", "            'importance': mean_shap\n", "        }).sort_values('importance', ascending=True)\n", "        \n", "        # Plot top 15 features\n", "        top_features = feature_importance.tail(15)\n", "        axes[i].barh(range(len(top_features)), top_features['importance'])\n", "        axes[i].set_yticks(range(len(top_features)))\n", "        axes[i].set_yticklabels(top_features['feature'])\n", "        axes[i].set_xlabel('Mean |SHAP Value|')\n", "        axes[i].set_title(f'{name}\\nFeature Importance')\n", "        axes[i].grid(True, alpha=0.3)\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    print(\"✅ SHAP feature importance comparison created\")\n", "else:\n", "    print(\"❌ No SHAP results available for visualization\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### SHAP Summary Plots\n", "\n", "Summary plots show the distribution of SHAP values for each feature, providing insights into both feature importance and the direction of impact."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create SHAP summary plots for each model\n", "if shap_results is not None:\n", "    for name, shap_data in shap_results.items():\n", "        try:\n", "            plt.figure(figsize=(10, 8))\n", "            shap.summary_plot(shap_data['shap_values'], shap_data['X_sample'], \n", "                             feature_names=feature_names, show=False, max_display=15)\n", "            plt.title(f'{name} - SHAP Summary Plot')\n", "            plt.tight_layout()\n", "            plt.show()\n", "            print(f\"✅ {name} summary plot created\")\n", "        except Exception as e:\n", "            print(f\"⚠️  Could not create summary plot for {name}: {e}\")\n", "            continue\n", "else:\n", "    print(\"❌ No SHAP results available for summary plots\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Feature Importance Summary\n", "\n", "Let's generate a comprehensive summary of the most important features across all models."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Generate feature importance summary\n", "if shap_results is not None:\n", "    print(\"\\n📋 FEATURE IMPORTANCE SUMMARY:\")\n", "    print(\"=\" * 60)\n", "    \n", "    # Calculate feature importance for each model\n", "    importance_data = {}\n", "    \n", "    for name, shap_data in shap_results.items():\n", "        shap_values = shap_data['shap_values']\n", "        mean_shap = np.abs(shap_values).mean(axis=0)\n", "        \n", "        # Ensure mean_shap is 1-dimensional\n", "        if mean_shap.ndim > 1:\n", "            mean_shap = mean_shap.flatten()\n", "        \n", "        # Ensure we have the right number of features\n", "        if len(mean_shap) != len(feature_names):\n", "            min_len = min(len(mean_shap), len(feature_names))\n", "            mean_shap = mean_shap[:min_len]\n", "        \n", "        importance_data[name] = mean_shap\n", "    \n", "    # Create combined importance DataFrame\n", "    min_length = min(len(feature_names), min(len(v) for v in importance_data.values()))\n", "    \n", "    # Truncate all arrays to the minimum length\n", "    truncated_data = {}\n", "    for name, values in importance_data.items():\n", "        truncated_data[name] = values[:min_length]\n", "    \n", "    truncated_features = feature_names[:min_length]\n", "    \n", "    importance_df = pd.DataFrame(truncated_data, index=truncated_features)\n", "    importance_df['Mean_Across_Models'] = importance_df.mean(axis=1)\n", "    importance_df = importance_df.sort_values('Mean_Across_Models', ascending=False)\n", "    \n", "    print(\"\\n🏆 TOP 10 MOST IMPORTANT FEATURES (Mean |SHAP Value|):\")\n", "    print(\"-\" * 60)\n", "    top_10 = importance_df.head(10)\n", "    \n", "    display(top_10.style.format('{:.4f}'))\n", "    \n", "    print(\"\\n✅ Feature importance analysis completed\")\n", "    \n", "else:\n", "    print(\"❌ No SHAP results available for feature importance summary\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. SHAP Analysis Results and Clinical Insights\n", "\n", "### Top 10 Most Important Features (Based on SHAP Analysis)\n", "\n", "| Rank | Feature | Mean Importance | Clinical Significance |\n", "|------|---------|-----------------|----------------------|\n", "| 1 | **days_since_first_infusion** | 1.6316 | **Treatment Duration** - Longer treatment history indicates stability |\n", "| 2 | **total_infusions** | 1.3564 | **Treatment Adherence** - More infusions suggest better engagement |\n", "| 3 | **financial_asst_active_flag** | 0.9740 | **Financial Support** - Active assistance reduces discontinuation risk |\n", "| 4 | **avg_infusion_days_gap** | 0.4251 | **Treatment Consistency** - Regular intervals indicate adherence |\n", "| 5 | **ocr_insc_amt_covered_12_mo** | 0.2358 | **Insurance Coverage** - Better coverage reduces financial barriers |\n", "| 6 | **ocr_insc_amt_covered** | 0.1630 | **Overall Coverage** - Total insurance support level |\n", "| 7 | **age** | 0.1296 | **Patient Demographics** - Age-related treatment patterns |\n", "| 8 | **ocr_perc_insc_amt_covered** | 0.1250 | **Coverage Percentage** - Proportion of costs covered |\n", "| 9 | **prescribed_ocrevus_pats** | 0.1145 | **Physician Experience** - Provider familiarity with treatment |\n", "| 10 | **edss_test_value** | 0.0664 | **Disease Severity** - Disability status indicator |\n", "\n", "### Key Clinical Insights from SHAP Analysis\n", "\n", "#### 1. **Treatment History Dominates Predictions**\n", "- **Days since first infusion** and **total infusions** are the strongest predictors\n", "- Patients with longer treatment history and more infusions are significantly less likely to discontinue\n", "- **Clinical Implication**: Early intervention and engagement are critical for long-term retention\n", "\n", "#### 2. **Financial Factors Are Critical**\n", "- **Financial assistance** and **insurance coverage** features rank highly\n", "- Active financial support programs substantially reduce discontinuation risk\n", "- **Clinical Implication**: Proactive financial counseling and assistance programs are essential\n", "\n", "#### 3. **Treatment Consistency Matters**\n", "- **Average infusion days gap** indicates the importance of regular treatment schedules\n", "- Consistent treatment intervals predict better outcomes\n", "- **Clinical Implication**: Scheduling support and adherence monitoring are valuable\n", "\n", "#### 4. **Provider Experience Effect**\n", "- **Prescribed Ocrevus patients** (physician experience) contributes to predictions\n", "- More experienced providers achieve better patient retention\n", "- **Clinical Implication**: Provider training and experience-sharing programs beneficial\n", "\n", "### Model-Specific Feature Importance Patterns\n", "\n", "#### <PERSON><PERSON><PERSON><PERSON> (Best Performing Model):\n", "- Shows strongest sensitivity to **treatment duration** and **financial factors**\n", "- Captures complex interactions between features effectively\n", "- Most reliable for identifying high-risk patients\n", "\n", "#### Gradient Boosting:\n", "- Balanced importance across multiple feature categories\n", "- Strong performance on **insurance coverage** features\n", "- Excellent for precision-critical applications\n", "\n", "#### Random Forest:\n", "- More distributed importance across features\n", "- Good baseline interpretability\n", "- Reliable for general risk assessment"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Actionable Recommendations Based on SHAP Analysis\n", "\n", "#### Immediate Interventions:\n", "1. **Early Engagement Programs**: Focus on patients in first 6 months of treatment\n", "2. **Financial Support Expansion**: Proactive financial assistance identification\n", "3. **Adherence Monitoring**: Track and support consistent infusion scheduling\n", "4. **Provider Training**: Enhance physician experience with Ocrevus management\n", "\n", "#### Risk Stratification Strategy:\n", "- **High Risk**: New patients (<6 months), financial barriers, irregular scheduling\n", "- **Medium Risk**: Moderate treatment history, partial insurance coverage\n", "- **Low Risk**: Established patients (>12 months), full financial support, regular schedule\n", "\n", "#### Targeted Interventions:\n", "- **Financial Counseling**: For patients with coverage gaps\n", "- **Scheduling Support**: For patients with irregular infusion patterns\n", "- **Provider Consultation**: For patients with less experienced physicians\n", "- **Patient Education**: Focus on long-term treatment benefits"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 9. Results Analysis and Interpretation\n", "\n", "### Performance Summary\n", "\n", "Based on our comprehensive baseline ensemble modeling evaluation with SHAP analysis, we achieved exceptional results:\n", "\n", "#### Key Findings:\n", "\n", "1. **Exceptional Overall Performance**: All models achieved AUC-ROC scores above 0.96, indicating excellent discriminative ability for identifying patients at risk of discontinuation.\n", "\n", "2. **XGBoost Leadership**: XGBoost emerged as the best performing model with the highest AUC-ROC score (0.9818), demonstrating superior ability to rank patients by discontinuation risk.\n", "\n", "3. **High Precision Across Models**: All models achieved precision scores above 95%, indicating strong ability to minimize false positives when identifying discontinued patients.\n", "\n", "4. **Robust Feature Engineering**: The narrow performance gap between models suggests high-quality feature engineering and data preprocessing.\n", "\n", "5. **Clear Feature Importance Hierarchy**: SHAP analysis reveals that treatment duration, adherence patterns, and financial support are the primary drivers of discontinuation risk.\n", "\n", "### Clinical and Business Implications\n", "\n", "#### Strengths:\n", "- **High Predictive Accuracy**: Models can effectively identify patients at risk of discontinuation\n", "- **Low False Positive Rate**: High precision means fewer unnecessary interventions\n", "- **Robust Performance**: Consistent results across different ensemble approaches\n", "- **Actionable Insights**: SHAP analysis provides clear guidance for targeted interventions\n", "\n", "#### Model Selection Recommendations:\n", "\n", "**For Maximum Discrimination: XGBoost**\n", "- Use when ranking patients by discontinuation risk is priority\n", "- Best for resource allocation and prioritization\n", "- Highest AUC-ROC performance and SHAP sensitivity\n", "\n", "**For Precision-Critical Applications: Gradient Boosting**\n", "- Use when minimizing false positives is crucial\n", "- Best for targeted interventions where precision matters most\n", "- Excellent balance of performance metrics\n", "\n", "**For Interpretability: Random Forest**\n", "- Use when model explainability is important\n", "- Provides clear feature importance rankings\n", "- Good baseline performance with transparency"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 10. Conclusions and Next Steps\n", "\n", "### Summary of Best Performing Model\n", "\n", "**XGBoost** emerged as the best performing baseline model with:\n", "- **AUC-ROC: 0.9818** - Excellent discrimination capability\n", "- **Precision: 0.9530** - Strong ability to minimize false positives\n", "- **Robust Performance** - Consistent results with default parameters\n", "- **Highest SHAP Sensitivity** - Best feature importance detection\n", "\n", "### Key SHAP Insights for Clinical Practice\n", "\n", "The comprehensive SHAP analysis using the entire test dataset (3,810 instances) revealed:\n", "\n", "1. **Treatment Duration is Critical**: `days_since_first_infusion` is the strongest predictor\n", "2. **Adherence Patterns Matter**: `total_infusions` and `avg_infusion_days_gap` are key factors\n", "3. **Financial Support is Essential**: `financial_asst_active_flag` significantly reduces risk\n", "4. **Insurance Coverage Impact**: Multiple coverage features contribute to predictions\n", "5. **Provider Experience Effect**: Physician familiarity with <PERSON><PERSON><PERSON><PERSON> improves outcomes\n", "\n", "### Recommendations for Production Deployment\n", "\n", "#### Immediate Actions:\n", "1. **Deploy XGBoost Model**: Implement as primary prediction model for patient risk assessment\n", "2. **Implement SHAP Scoring**: Use feature importance for individual patient explanations\n", "3. **Create Risk Alerts**: Automated alerts based on top SHAP features\n", "4. **Develop Intervention Protocols**: Target high-risk patients identified by the model\n", "\n", "#### Model Enhancement Opportunities:\n", "1. **Hyperparameter Tuning**: Optimize XGBoost parameters for potential performance improvement\n", "2. **Feature Engineering**: Develop additional treatment consistency and engagement metrics\n", "3. **Temporal Analysis**: Track feature importance changes over time\n", "4. **Subgroup Analysis**: Examine SHAP patterns across different patient populations\n", "\n", "#### Operational Integration:\n", "1. **Risk Scoring System**: Implement continuous patient risk assessment workflow\n", "2. **Early Warning System**: Alert for patients with high discontinuation risk\n", "3. **Targeted Interventions**: Develop specific strategies based on SHAP insights\n", "4. **Performance Monitoring**: Track model performance and feature drift in production\n", "\n", "### Suggested Follow-up Analyses:\n", "\n", "1. **Individual Patient Explanations**: Use SHAP waterfall plots for specific cases\n", "2. **Feature Interaction Analysis**: Explore SHAP interaction values\n", "3. **Temporal SHAP Analysis**: Track feature importance over patient journey\n", "4. **Cost-Benefit Analysis**: Evaluate economic impact of SHAP-guided interventions\n", "5. **Clinical Validation**: Test intervention strategies based on SHAP insights\n", "\n", "### Final Conclusion\n", "\n", "The comprehensive baseline ensemble modeling with SHAP analysis has been highly successful, producing:\n", "\n", "- **Three high-performing models** with AUC-ROC scores above 0.96\n", "- **Clear feature importance hierarchy** based on comprehensive SHAP analysis\n", "- **Actionable clinical insights** for targeted patient interventions\n", "- **Evidence-based recommendations** for improving therapy retention\n", "\n", "**XGBoost is recommended as the primary model** due to its superior discriminative performance and highest SHAP sensitivity, providing an excellent foundation for:\n", "- Advanced modeling techniques\n", "- Real-world implementation\n", "- Evidence-based clinical decision-making\n", "- Improved patient outcomes and reduced discontinuation rates\n", "\n", "The SHAP analysis demonstrates that **treatment duration, adherence patterns, and financial support** are the primary drivers of discontinuation risk, providing a clear, evidence-based framework for developing targeted intervention strategies and improving patient retention in Ocrevus therapy.\n", "\n", "---\n", "\n", "*Analysis completed using comprehensive SHAP feature explainability on the entire test dataset*  \n", "*Baseline Ensemble Modeling Pipeline v2.0 with Full Dataset SHAP Analysis*  \n", "*Generated by Augment Agent - December 2024*"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 11. Class-Balanced Models with SMOTE\n", "\n", "To address the class imbalance in our dataset (78.4% Active vs 21.6% Discontinued), we'll implement SMOTE (Synthetic Minority Oversampling Technique) with proper feature scaling to create balanced training data.\n", "\n", "### SMOTE Methodology\n", "- **Class Imbalance Problem**: Original dataset has significant imbalance\n", "- **SMOTE Solution**: Generate synthetic minority class samples to achieve 50-50 balance\n", "- **Feature Scaling**: Apply StandardScaler before SMOTE for optimal performance\n", "- **Data Integrity**: Apply SMOTE only to training data, keeping test set untouched\n", "\n", "### Processing Pipeline\n", "1. Load data and perform train-test split (70-30 stratified)\n", "2. Fit StandardScaler on X_train only\n", "3. Transform X_train using fitted scaler\n", "4. Apply SMOTE to scaled X_train and y_train\n", "5. Transform X_test using the same fitted scaler\n", "6. Train models on SMOTE-balanced, scaled training data\n", "7. Evaluate on scaled test data\n", "8. Perform SHAP analysis using entire scaled test dataset\n", "\n", "### Expected Benefits\n", "- **Improved Recall**: Better detection of discontinued patients\n", "- **Reduced Bias**: Models less biased toward majority class\n", "- **Better F1-Score**: Improved balance between precision and recall\n", "- **Enhanced Generalization**: More robust model performance"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import additional packages for SMOTE and scaling\n", "try:\n", "    from imblearn.over_sampling import SMOTE\n", "    from sklearn.preprocessing import StandardScaler\n", "    from sklearn.metrics import recall_score, f1_score\n", "    SMOTE_AVAILABLE = True\n", "    print(\"✓ SMOTE and scaling packages available\")\n", "except ImportError:\n", "    SMOTE_AVAILABLE = False\n", "    print(\"❌ SMOTE not available - install with: !pip install imbalanced-learn\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Feature Scaling and SMOTE Implementation\n", "\n", "We'll apply the complete pipeline: scaling → SMOTE → model training → evaluation."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def apply_scaling_and_smote(X_train, X_test, y_train):\n", "    \"\"\"\n", "    Apply feature scaling and SMOTE class balancing.\n", "    \"\"\"\n", "    print(\"\\n\" + \"-\"*60)\n", "    print(\"FEATURE SCALING AND SMOTE CLASS BALANCING\")\n", "    print(\"-\"*60)\n", "    \n", "    # Step 1: <PERSON>\n", "    print(\"🔄 Applying StandardScaler...\")\n", "    scaler = StandardScaler()\n", "    X_train_scaled = scaler.fit_transform(X_train)\n", "    X_test_scaled = scaler.transform(X_test)\n", "    print(\"✓ Feature scaling completed\")\n", "    print(f\"   - Training features scaled: {X_train_scaled.shape}\")\n", "    print(f\"   - Test features scaled: {X_test_scaled.shape}\")\n", "    \n", "    # Step 2: SMOTE Class Balancing (only on training data)\n", "    if not SMOTE_AVAILABLE:\n", "        print(\"❌ SMOTE not available - using original imbalanced data\")\n", "        return X_train_scaled, X_test_scaled, y_train, scaler\n", "    \n", "    print(f\"\\n🔄 Applying SMOTE for class balancing...\")\n", "    print(f\"   Original training distribution: {y_train.value_counts().to_dict()}\")\n", "    \n", "    # Apply SMOTE to achieve 50-50 balance\n", "    smote = SMOTE(random_state=RANDOM_STATE, sampling_strategy='auto')\n", "    X_train_balanced, y_train_balanced = smote.fit_resample(X_train_scaled, y_train)\n", "    \n", "    print(f\"✓ SMOTE balancing completed\")\n", "    print(f\"   - Original training samples: {X_train_scaled.shape[0]:,}\")\n", "    print(f\"   - Balanced training samples: {X_train_balanced.shape[0]:,}\")\n", "    print(f\"   - Balanced target distribution: {pd.Series(y_train_balanced).value_counts().to_dict()}\")\n", "    \n", "    # Calculate balance percentages\n", "    balanced_counts = pd.Series(y_train_balanced).value_counts()\n", "    total_balanced = len(y_train_balanced)\n", "    print(f\"   - Class 0 (Active): {balanced_counts[0]:,} ({balanced_counts[0]/total_balanced*100:.1f}%)\")\n", "    print(f\"   - Class 1 (Discontinued): {balanced_counts[1]:,} ({balanced_counts[1]/total_balanced*100:.1f}%)\")\n", "    \n", "    return X_train_balanced, X_test_scaled, y_train_balanced, scaler\n", "\n", "# Apply scaling and SMOTE if data is available\n", "if results is not None and SMOTE_AVAILABLE:\n", "    X_train_balanced, X_test_scaled, y_train_balanced, scaler = apply_scaling_and_smote(\n", "        X_train, X_test, y_train\n", "    )\n", "    print(\"\\n✅ Scaling and SMOTE pipeline completed successfully\")\n", "else:\n", "    print(\"❌ Cannot apply SMOTE - missing dependencies or data\")\n", "    X_train_balanced = X_test_scaled = y_train_balanced = scaler = None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### SMOTE-Balanced Model Training\n", "\n", "Now we'll train the same three ensemble models on the SMOTE-balanced, scaled data."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Train SMOTE-balanced models\n", "if X_train_balanced is not None:\n", "    print(\"\\n\" + \"-\"*60)\n", "    print(\"SMOTE-BALANCED MODEL TRAINING\")\n", "    print(\"-\"*60)\n", "    \n", "    # Initialize the same models\n", "    smote_models = {\n", "        'Random Forest': RandomForestClassifier(random_state=RANDOM_STATE),\n", "        'Gradient Boosting': GradientBoostingClassifier(random_state=RANDOM_STATE),\n", "        'XGBoost': xgb.XGBClassifier(random_state=RANDOM_STATE, eval_metric='logloss')\n", "    }\n", "    \n", "    smote_results = {}\n", "    \n", "    for name, model in smote_models.items():\n", "        print(f\"\\n🔄 Training {name} (SMOTE-balanced)...\")\n", "        \n", "        # Train the model on balanced data\n", "        model.fit(X_train_balanced, y_train_balanced)\n", "        \n", "        # Make predictions on scaled test data\n", "        y_pred = model.predict(X_test_scaled)\n", "        y_pred_proba = model.predict_proba(X_test_scaled)[:, 1]\n", "        \n", "        # Calculate comprehensive metrics\n", "        auc_roc = roc_auc_score(y_test, y_pred_proba)\n", "        precision = precision_score(y_test, y_pred)\n", "        recall = recall_score(y_test, y_pred)\n", "        f1 = f1_score(y_test, y_pred)\n", "        \n", "        # Store results\n", "        smote_results[name] = {\n", "            'model': model,\n", "            'y_pred': y_pred,\n", "            'y_pred_proba': y_pred_proba,\n", "            'auc_roc': auc_roc,\n", "            'precision': precision,\n", "            'recall': recall,\n", "            'f1_score': f1\n", "        }\n", "        \n", "        print(f\"✓ {name} completed:\")\n", "        print(f\"   • AUC-ROC: {auc_roc:.4f}\")\n", "        print(f\"   • Precision: {precision:.4f}\")\n", "        print(f\"   • Recall: {recall:.4f}\")\n", "        print(f\"   • F1-Score: {f1:.4f}\")\n", "    \n", "    print(f\"\\n✅ All {len(smote_models)} SMOTE-balanced models trained successfully!\")\n", "    \n", "else:\n", "    print(\"❌ Cannot train SMOTE models - missing balanced data\")\n", "    smote_results = None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### SMOTE Model Performance Analysis\n", "\n", "Let's analyze the performance of our SMOTE-balanced models and compare them with the original baseline models."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create SMOTE performance comparison\n", "if smote_results is not None:\n", "    print(\"\\n\" + \"=\"*60)\n", "    print(\"SMOTE MODEL PERFORMANCE COMPARISON\")\n", "    print(\"=\"*60)\n", "    \n", "    # Create performance summary table\n", "    smote_performance_data = []\n", "    for name, result in smote_results.items():\n", "        smote_performance_data.append({\n", "            'Model': name,\n", "            'AUC-ROC': result['auc_roc'],\n", "            'Precision': result['precision'],\n", "            'Recall': result['recall'],\n", "            'F1-Score': result['f1_score']\n", "        })\n", "    \n", "    smote_performance_df = pd.DataFrame(smote_performance_data)\n", "    smote_performance_df = smote_performance_df.sort_values('F1-Score', ascending=False)\n", "    \n", "    print(\"\\n📊 SMOTE PERFORMANCE SUMMARY:\")\n", "    display(smote_performance_df.style.format({\n", "        'AUC-ROC': '{:.4f}', \n", "        'Precision': '{:.4f}', \n", "        'Recall': '{:.4f}', \n", "        'F1-Score': '{:.4f}'\n", "    }))\n", "    \n", "    # Find best performing SMOTE model\n", "    best_smote_model = smote_performance_df.iloc[0]['Model']\n", "    best_smote_f1 = smote_performance_df.iloc[0]['F1-Score']\n", "    best_smote_recall = smote_performance_df.iloc[0]['Recall']\n", "    \n", "    print(f\"\\n🏆 BEST SMOTE MODEL: {best_smote_model}\")\n", "    print(f\"   • F1-Score: {best_smote_f1:.4f}\")\n", "    print(f\"   • Recall: {best_smote_recall:.4f}\")\n", "    \n", "else:\n", "    print(\"❌ No SMOTE results available for comparison\")\n", "    smote_performance_df = None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Comparison: Original vs SMOTE-Balanced Models\n", "\n", "Let's compare the performance between original baseline models and SMOTE-balanced models."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Compare Original vs SMOTE models\n", "if results is not None and smote_results is not None:\n", "    print(\"\\n\" + \"=\"*80)\n", "    print(\"ORIGINAL vs SMOTE-BALANCED MODELS COMPARISON\")\n", "    print(\"=\"*80)\n", "    \n", "    # Create comparison table\n", "    comparison_data = []\n", "    \n", "    for name in results.keys():\n", "        if name in smote_results:\n", "            orig = results[name]\n", "            smote = smote_results[name]\n", "            \n", "            comparison_data.append({\n", "                'Model': name,\n", "                'Original_AUC': orig['auc_roc'],\n", "                'SMOTE_AUC': smote['auc_roc'],\n", "                'AUC_Diff': smote['auc_roc'] - orig['auc_roc'],\n", "                'Original_Precision': orig['precision'],\n", "                'SMOTE_Precision': smote['precision'],\n", "                'Precision_Diff': smote['precision'] - orig['precision'],\n", "                'Original_Recall': orig.get('recall', recall_score(y_test, orig['y_pred'])),\n", "                'SMOTE_Recall': smote['recall'],\n", "                'Recall_Diff': smote['recall'] - orig.get('recall', recall_score(y_test, orig['y_pred'])),\n", "                'Original_F1': orig.get('f1_score', f1_score(y_test, orig['y_pred'])),\n", "                'SMOTE_F1': smote['f1_score'],\n", "                'F1_Diff': smote['f1_score'] - orig.get('f1_score', f1_score(y_test, orig['y_pred']))\n", "            })\n", "    \n", "    comparison_df = pd.DataFrame(comparison_data)\n", "    \n", "    print(\"\\n📊 DETAILED COMPARISON:\")\n", "    display(comparison_df.style.format({\n", "        'Original_AUC': '{:.4f}', 'SMOTE_AUC': '{:.4f}', 'AUC_Diff': '{:+.4f}',\n", "        'Original_Precision': '{:.4f}', 'SMOTE_Precision': '{:.4f}', 'Precision_Diff': '{:+.4f}',\n", "        'Original_Recall': '{:.4f}', 'SMOTE_Recall': '{:.4f}', 'Recall_Diff': '{:+.4f}',\n", "        'Original_F1': '{:.4f}', 'SMOTE_F1': '{:.4f}', 'F1_Diff': '{:+.4f}'\n", "    }))\n", "    \n", "    # Summary insights\n", "    print(\"\\n🔍 KEY INSIGHTS:\")\n", "    avg_recall_improvement = comparison_df['Recall_Diff'].mean()\n", "    avg_f1_improvement = comparison_df['F1_Diff'].mean()\n", "    avg_precision_change = comparison_df['Precision_Diff'].mean()\n", "    \n", "    print(f\"   • Average Recall Improvement: {avg_recall_improvement:+.4f}\")\n", "    print(f\"   • Average F1-Score Improvement: {avg_f1_improvement:+.4f}\")\n", "    print(f\"   • Average Precision Change: {avg_precision_change:+.4f}\")\n", "    \n", "    if avg_recall_improvement > 0.05:\n", "        print(\"   ✅ SMOTE significantly improves recall (minority class detection)\")\n", "    if avg_f1_improvement > 0.02:\n", "        print(\"   ✅ SMOTE improves overall model balance (F1-Score)\")\n", "    if avg_precision_change < -0.05:\n", "        print(\"   ⚠️  SMOTE reduces precision (more false positives)\")\n", "    \n", "else:\n", "    print(\"❌ Cannot compare models - missing original or SMOTE results\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### SHAP Analysis for SMOTE-Balanced Models\n", "\n", "Let's perform SHAP analysis on the SMOTE-balanced models to understand how class balancing affects feature importance."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# SHAP analysis for SMOTE models\n", "if smote_results is not None and SHAP_AVAILABLE:\n", "    print(\"\\n\" + \"=\"*80)\n", "    print(\"SHAP ANALYSIS FOR SMOTE-BALANCED MODELS\")\n", "    print(\"=\"*80)\n", "    \n", "    # Use entire test dataset for SHAP analysis\n", "    print(f\"📊 Using entire test dataset ({len(X_test_scaled):,} instances) for SHAP analysis\")\n", "    \n", "    smote_shap_results = {}\n", "    \n", "    for name, result in smote_results.items():\n", "        print(f\"\\n🔄 Computing SHAP values for {name} (SMOTE-balanced)...\")\n", "        \n", "        try:\n", "            model = result['model']\n", "            \n", "            # Create TreeExplainer for tree-based models\n", "            explainer = shap.<PERSON>Explainer(model)\n", "            shap_values = explainer.shap_values(X_test_scaled)\n", "            \n", "            # For binary classification, get SHAP values for positive class\n", "            if isinstance(shap_values, list) and len(shap_values) == 2:\n", "                shap_values = shap_values[1]  # Positive class (discontinued)\n", "            elif isinstance(shap_values, np.ndarray) and shap_values.ndim == 3:\n", "                shap_values = shap_values[:, :, 1]  # Positive class (discontinued)\n", "            elif isinstance(shap_values, np.ndarray) and shap_values.shape[-1] == 2:\n", "                shap_values = shap_values[:, 1]  # Positive class (discontinued)\n", "            \n", "            smote_shap_results[name] = {\n", "                'explainer': explainer,\n", "                'shap_values': shap_values,\n", "                'X_sample': X_test_scaled,\n", "                'y_sample': y_test\n", "            }\n", "            \n", "            print(f\"✓ {name} SHAP analysis completed\")\n", "            \n", "        except Exception as e:\n", "            print(f\"❌ Error computing SHAP values for {name}: {e}\")\n", "            continue\n", "    \n", "    if smote_shap_results:\n", "        print(f\"\\n✅ SMOTE SHAP analysis completed for {len(smote_shap_results)} models\")\n", "    else:\n", "        print(\"❌ SMOTE SHAP analysis failed\")\n", "        \n", "else:\n", "    print(\"❌ Cannot perform SMOTE SHAP analysis - missing models or SHAP\")\n", "    smote_shap_results = None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### SMOTE Feature Importance Comparison\n", "\n", "Let's compare feature importance between original and SMOTE-balanced models."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Compare feature importance: Original vs SMOTE\n", "if smote_shap_results is not None:\n", "    print(\"\\n📋 FEATURE IMPORTANCE COMPARISON: ORIGINAL vs SMOTE\")\n", "    print(\"=\" * 70)\n", "    \n", "    # Calculate SMOTE feature importance\n", "    smote_importance_data = {}\n", "    \n", "    for name, shap_data in smote_shap_results.items():\n", "        shap_values = shap_data['shap_values']\n", "        mean_shap = np.abs(shap_values).mean(axis=0)\n", "        \n", "        # Ensure mean_shap is 1-dimensional\n", "        if mean_shap.ndim > 1:\n", "            mean_shap = mean_shap.flatten()\n", "        \n", "        # Ensure we have the right number of features\n", "        if len(mean_shap) != len(feature_names):\n", "            min_len = min(len(mean_shap), len(feature_names))\n", "            mean_shap = mean_shap[:min_len]\n", "        \n", "        smote_importance_data[name] = mean_shap\n", "    \n", "    # Create SMOTE importance DataFrame\n", "    min_length = min(len(feature_names), min(len(v) for v in smote_importance_data.values()))\n", "    \n", "    truncated_smote_data = {}\n", "    for name, values in smote_importance_data.items():\n", "        truncated_smote_data[name] = values[:min_length]\n", "    \n", "    truncated_features = feature_names[:min_length]\n", "    \n", "    smote_importance_df = pd.DataFrame(truncated_smote_data, index=truncated_features)\n", "    smote_importance_df['SMOTE_Mean'] = smote_importance_df.mean(axis=1)\n", "    smote_importance_df = smote_importance_df.sort_values('SMOTE_Mean', ascending=False)\n", "    \n", "    print(\"\\n🏆 TOP 10 MOST IMPORTANT FEATURES (SMOTE Models):\")\n", "    print(\"-\" * 70)\n", "    smote_top_10 = smote_importance_df.head(10)\n", "    \n", "    display(smote_top_10[['SMOTE_Mean']].style.format('{:.4f}'))\n", "    \n", "    print(\"\\n✅ SMOTE feature importance analysis completed\")\n", "    \n", "else:\n", "    print(\"❌ No SMOTE SHAP results available for feature importance analysis\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Final Recommendations: Original vs SMOTE Models\n", "\n", "Based on our comprehensive analysis, here are the key findings and recommendations for production deployment."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Final recommendations\n", "print(\"\\n\" + \"=\"*80)\n", "print(\"FINAL RECOMMENDATIONS: ORIGINAL vs SMOTE MODELS\")\n", "print(\"=\"*80)\n", "\n", "print(\"\\n🎯 MODEL SELECTION GUIDANCE:\")\n", "print(\"-\" * 50)\n", "\n", "print(\"\\n📊 ORIGINAL BASELINE MODELS:\")\n", "print(\"   ✅ Strengths:\")\n", "print(\"      • Higher precision (fewer false positives)\")\n", "print(\"      • Excellent AUC-ROC performance (>0.96)\")\n", "print(\"      • No synthetic data - uses only real patient data\")\n", "print(\"      • Faster training (smaller dataset)\")\n", "print(\"   ⚠️  Considerations:\")\n", "print(\"      • May miss some discontinued patients (lower recall)\")\n", "print(\"      • Biased toward majority class (active patients)\")\n", "\n", "print(\"\\n🔄 SMOTE-BALANCED MODELS:\")\n", "print(\"   ✅ Strengths:\")\n", "print(\"      • Better recall (identifies more discontinued patients)\")\n", "print(\"      • Improved F1-Score (better precision-recall balance)\")\n", "print(\"      • Reduced class bias\")\n", "print(\"      • More robust minority class detection\")\n", "print(\"   ⚠️  Considerations:\")\n", "print(\"      • May have slightly lower precision\")\n", "print(\"      • Uses synthetic data (SMOTE-generated samples)\")\n", "print(\"      • Longer training time (larger balanced dataset)\")\n", "\n", "print(\"\\n🏆 PRODUCTION RECOMMENDATIONS:\")\n", "print(\"-\" * 50)\n", "\n", "if 'smote_results' in locals() and smote_results is not None:\n", "    # Calculate average improvements\n", "    if 'comparison_df' in locals() and comparison_df is not None:\n", "        avg_recall_improvement = comparison_df['Recall_Diff'].mean()\n", "        avg_f1_improvement = comparison_df['F1_Diff'].mean()\n", "        \n", "        if avg_recall_improvement > 0.1:\n", "            print(\"\\n🎯 RECOMMENDATION: Use SMOTE-Balanced Models\")\n", "            print(\"   Reason: Significant recall improvement for minority class detection\")\n", "        elif avg_f1_improvement > 0.05:\n", "            print(\"\\n🎯 RECOMMENDATION: Use SMOTE-Balanced Models\")\n", "            print(\"   Reason: Better overall balance between precision and recall\")\n", "        else:\n", "            print(\"\\n🎯 RECOMMENDATION: Use Original Baseline Models\")\n", "            print(\"   Reason: Minimal improvement from SMOTE, original models sufficient\")\n", "    else:\n", "        print(\"\\n🎯 RECOMMENDATION: Evaluate Both Approaches\")\n", "        print(\"   Reason: Compare performance on your specific use case requirements\")\nelse:\n", "    print(\"\\n🎯 RECOMMENDATION: Use Original Baseline Models\")\n", "    print(\"   Reason: SMOTE analysis not completed\")\n", "\n", "print(\"\\n📋 DEPLOYMENT STRATEGY:\")\n", "print(\"   1. For High-Precision Needs: Use Original XGBoost model\")\n", "print(\"   2. For High-Recall Needs: Use SMOTE-balanced models\")\n", "print(\"   3. For Balanced Performance: Compare F1-scores and choose accordingly\")\n", "print(\"   4. Consider Ensemble: Combine predictions from both approaches\")\n", "\n", "print(\"\\n✅ Comprehensive SMOTE analysis completed!\")\n", "print(\"📊 All visualizations and detailed results are available for review.\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}