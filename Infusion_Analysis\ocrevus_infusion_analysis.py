import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns

# 1. Load the dataset
df = pd.read_csv('ocrevus_switch_alerts.csv')

# 2. Generate descriptive statistics by patientsubstatus
# Ensure patientsubstatus is simplified to just 'Active' or 'Discontinued'
df['status'] = df['patientsubstatus'].apply(lambda x: 'Discontinued' if 'Discontinued' in str(x) else 'Active')

# Calculate descriptive statistics
stats = df.groupby('status')['total_infusions'].describe()
print("Descriptive Statistics for Total Infusions by Patient Status:")
print(stats)

# 3. Create visualization
plt.figure(figsize=(10, 6))

# Bar plot showing mean infusions by status with error bars
ax = sns.barplot(x='status', y='total_infusions', data=df, errorbar=('ci', 95), palette='viridis')

# Add mean values as text annotations
for i, status in enumerate(stats.index):
    mean_val = stats.loc[status, 'mean']
    ax.text(i, mean_val + 0.1, f'Mean: {mean_val:.2f}', ha='center')

# Add labels and title
plt.title('Comparison of Total Infusions Between Active and Discontinued Patients', fontsize=14)
plt.xlabel('Patient Status', fontsize=12)
plt.ylabel('Number of Infusions', fontsize=12)
plt.tight_layout()

# 4. Add a boxplot for distribution comparison
plt.figure(figsize=(10, 6))
sns.boxplot(x='status', y='total_infusions', data=df, palette='viridis')
plt.title('Distribution of Total Infusions by Patient Status', fontsize=14)
plt.xlabel('Patient Status', fontsize=12)
plt.ylabel('Number of Infusions', fontsize=12)
plt.tight_layout()

# Show plots
plt.show()

# 5. Statistical significance test
from scipy import stats as stats_test
active = df[df['status'] == 'Active']['total_infusions']
discontinued = df[df['status'] == 'Discontinued']['total_infusions']
t_stat, p_value = stats_test.ttest_ind(active, discontinued, equal_var=False)

print(f"\nT-test Results (Active vs Discontinued):")
print(f"t-statistic: {t_stat:.4f}")
print(f"p-value: {p_value:.4f}")
print(f"Statistically significant difference: {p_value < 0.05}")

# 6. Brief interpretation
print("\nInterpretation:")
if stats.loc['Discontinued', 'mean'] > stats.loc['Active', 'mean']:
    print("Patients who discontinued therapy had a higher average number of infusions.")
    print("This suggests patients may be more likely to discontinue after receiving more infusions.")
else:
    print("Patients who remained active had a higher average number of infusions.")
    print("This suggests the number of infusions may not be a driving factor for discontinuation.")