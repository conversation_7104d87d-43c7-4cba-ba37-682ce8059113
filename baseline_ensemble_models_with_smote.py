"""
Baseline Ensemble Models with SMOTE for Ocrevus Discontinuation Prediction

This script builds and evaluates three baseline ensemble models with SMOTE class balancing:
1. Random Forest
2. Gradient Boosted Decision Trees
3. XGBoost

Key Features:
- SMOTE (Synthetic Minority Oversampling Technique) for class balancing
- StandardScaler for feature scaling
- Comprehensive SHAP analysis using entire test dataset
- Comparison with original baseline models

Author: Augment Agent
Date: 2025
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import roc_auc_score, precision_score, recall_score, f1_score
from sklearn.metrics import classification_report, confusion_matrix
from sklearn.metrics import roc_curve, precision_recall_curve
import xgboost as xgb

# SMOTE for class balancing
try:
    from imblearn.over_sampling import SMOTE
    SMOTE_AVAILABLE = True
    print("✓ SMOTE available for class balancing")
except ImportError:
    SMOTE_AVAILABLE = False
    print("❌ SMOTE not available - install with: python -m pip install imbalanced-learn")

# SHAP for feature explainability
try:
    import shap
    SHAP_AVAILABLE = True
    print("✓ SHAP available for feature explainability analysis")
except ImportError:
    SHAP_AVAILABLE = False
    print("⚠️  SHAP not available - install with: python -m pip install shap")

import warnings
warnings.filterwarnings('ignore')

# Set random seed for reproducibility
RANDOM_STATE = 42
np.random.seed(RANDOM_STATE)

def load_and_preprocess_data(file_path='ocrevus_feat_eng_output.csv'):
    """
    Load the feature engineering output and prepare for modeling with SMOTE.
    
    Parameters:
    -----------
    file_path : str
        Path to the feature engineering output CSV file
        
    Returns:
    --------
    X_train_scaled, X_test_scaled, y_train_balanced, y_test : arrays
        Processed data ready for modeling
    feature_names : list
        List of feature column names
    scaler : StandardScaler
        Fitted scaler for future use
    """
    print("=" * 80)
    print("BASELINE ENSEMBLE MODELS WITH SMOTE FOR OCREVUS DISCONTINUATION PREDICTION")
    print("=" * 80)
    
    # Load the dataset
    try:
        df = pd.read_csv(file_path)
        print(f"✓ Dataset loaded successfully: {df.shape[0]:,} rows × {df.shape[1]} columns")
    except FileNotFoundError:
        print(f"❌ Error: File '{file_path}' not found.")
        return None, None, None, None, None, None
    except Exception as e:
        print(f"❌ Error loading data: {e}")
        return None, None, None, None, None, None
    
    # Data preprocessing
    print("\n" + "-" * 60)
    print("DATA PREPROCESSING")
    print("-" * 60)
    
    # Remove specified columns
    columns_to_exclude = ['patientid', 'latest_infusion_dt', 'next_estimated_infusion_date']
    print(f"Excluding columns: {columns_to_exclude}")
    
    # Check if columns exist before dropping
    existing_exclude_cols = [col for col in columns_to_exclude if col in df.columns]
    if existing_exclude_cols:
        df = df.drop(columns=existing_exclude_cols)
        print(f"✓ Removed {len(existing_exclude_cols)} columns")
    
    # Separate features and target
    target_col = 'discontinue_flag'
    if target_col not in df.columns:
        print(f"❌ Error: Target column '{target_col}' not found in dataset")
        return None, None, None, None, None, None
    
    X = df.drop(columns=[target_col])
    y = df[target_col]
    feature_names = X.columns.tolist()
    
    print(f"✓ Features: {X.shape[1]} columns")
    print(f"✓ Target variable: {target_col}")
    print(f"✓ Original target distribution:")
    print(f"   - Class 0 (Active): {(y == 0).sum():,} ({(y == 0).mean()*100:.1f}%)")
    print(f"   - Class 1 (Discontinued): {(y == 1).sum():,} ({(y == 1).mean()*100:.1f}%)")
    
    # Check for missing values
    missing_values = X.isnull().sum().sum()
    if missing_values > 0:
        print(f"⚠️  Warning: {missing_values} missing values found")
        X = X.fillna(X.median())
        print("✓ Missing values filled with median")
    else:
        print("✓ No missing values found")
    
    # Perform stratified train-test split (70-30)
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, 
        test_size=0.3, 
        random_state=RANDOM_STATE, 
        stratify=y
    )
    
    print(f"\n✓ Train-test split completed:")
    print(f"   - Training set: {X_train.shape[0]:,} samples ({X_train.shape[0]/len(df)*100:.1f}%)")
    print(f"   - Test set: {X_test.shape[0]:,} samples ({X_test.shape[0]/len(df)*100:.1f}%)")
    print(f"   - Training target distribution: {y_train.value_counts().to_dict()}")
    print(f"   - Test target distribution: {y_test.value_counts().to_dict()}")
    
    return X_train, X_test, y_train, y_test, feature_names, df

def apply_scaling_and_smote(X_train, X_test, y_train):
    """
    Apply feature scaling and SMOTE class balancing.
    
    Parameters:
    -----------
    X_train, X_test : DataFrames
        Training and test features
    y_train : Series
        Training target
        
    Returns:
    --------
    X_train_scaled, X_test_scaled : arrays
        Scaled features
    y_train_balanced : array
        Balanced target after SMOTE
    scaler : StandardScaler
        Fitted scaler
    """
    print("\n" + "-" * 60)
    print("FEATURE SCALING AND SMOTE CLASS BALANCING")
    print("-" * 60)
    
    # Step 1: Feature Scaling
    print("🔄 Applying StandardScaler...")
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)
    print("✓ Feature scaling completed")
    print(f"   - Training features scaled: {X_train_scaled.shape}")
    print(f"   - Test features scaled: {X_test_scaled.shape}")
    
    # Step 2: SMOTE Class Balancing (only on training data)
    if not SMOTE_AVAILABLE:
        print("❌ SMOTE not available - using original imbalanced data")
        return X_train_scaled, X_test_scaled, y_train, scaler
    
    print(f"\n🔄 Applying SMOTE for class balancing...")
    print(f"   Original training distribution: {y_train.value_counts().to_dict()}")
    
    # Apply SMOTE to achieve 50-50 balance
    smote = SMOTE(random_state=RANDOM_STATE, sampling_strategy='auto')
    X_train_balanced, y_train_balanced = smote.fit_resample(X_train_scaled, y_train)
    
    print(f"✓ SMOTE balancing completed")
    print(f"   - Original training samples: {X_train_scaled.shape[0]:,}")
    print(f"   - Balanced training samples: {X_train_balanced.shape[0]:,}")
    print(f"   - Balanced target distribution: {pd.Series(y_train_balanced).value_counts().to_dict()}")
    
    # Calculate balance percentages
    balanced_counts = pd.Series(y_train_balanced).value_counts()
    total_balanced = len(y_train_balanced)
    print(f"   - Class 0 (Active): {balanced_counts[0]:,} ({balanced_counts[0]/total_balanced*100:.1f}%)")
    print(f"   - Class 1 (Discontinued): {balanced_counts[1]:,} ({balanced_counts[1]/total_balanced*100:.1f}%)")
    
    return X_train_balanced, X_test_scaled, y_train_balanced, scaler

def build_baseline_models():
    """
    Initialize baseline ensemble models with default parameters.

    Returns:
    --------
    dict
        Dictionary containing model instances
    """
    print("\n" + "-" * 60)
    print("INITIALIZING BASELINE MODELS")
    print("-" * 60)

    models = {}

    # Random Forest
    models['Random Forest'] = RandomForestClassifier(random_state=RANDOM_STATE)
    print("✓ Random Forest initialized")

    # Gradient Boosting
    models['Gradient Boosting'] = GradientBoostingClassifier(random_state=RANDOM_STATE)
    print("✓ Gradient Boosting initialized")

    # XGBoost
    models['XGBoost'] = xgb.XGBClassifier(random_state=RANDOM_STATE, eval_metric='logloss')
    print("✓ XGBoost initialized")

    print(f"\n📋 INITIALIZED {len(models)} BASELINE ENSEMBLE MODELS:")
    for name, model in models.items():
        print(f"   • {name}: {type(model).__name__}")

    return models

def train_and_evaluate_models(models, X_train, X_test, y_train, y_test):
    """
    Train all models and evaluate their performance with comprehensive metrics.

    Parameters:
    -----------
    models : dict
        Dictionary of model instances
    X_train, X_test, y_train, y_test : arrays
        Training and test data

    Returns:
    --------
    dict
        Dictionary containing trained models and results
    """
    print("\n" + "-" * 60)
    print("TRAINING AND EVALUATION")
    print("-" * 60)

    results = {}

    for name, model in models.items():
        print(f"\n🔄 Training {name}...")

        # Train the model
        model.fit(X_train, y_train)

        # Make predictions
        y_pred = model.predict(X_test)
        y_pred_proba = model.predict_proba(X_test)[:, 1]

        # Calculate comprehensive metrics
        auc_roc = roc_auc_score(y_test, y_pred_proba)
        precision = precision_score(y_test, y_pred)
        recall = recall_score(y_test, y_pred)
        f1 = f1_score(y_test, y_pred)

        # Store results
        results[name] = {
            'model': model,
            'y_pred': y_pred,
            'y_pred_proba': y_pred_proba,
            'auc_roc': auc_roc,
            'precision': precision,
            'recall': recall,
            'f1_score': f1
        }

        print(f"✓ {name} completed:")
        print(f"   • AUC-ROC: {auc_roc:.4f}")
        print(f"   • Precision: {precision:.4f}")
        print(f"   • Recall: {recall:.4f}")
        print(f"   • F1-Score: {f1:.4f}")

    return results

def create_performance_comparison(results, y_test):
    """
    Create comprehensive performance comparison and visualizations.

    Parameters:
    -----------
    results : dict
        Dictionary containing model results
    y_test : array
        True test labels
    """
    print("\n" + "=" * 60)
    print("MODEL PERFORMANCE COMPARISON (SMOTE-BALANCED)")
    print("=" * 60)

    # Create performance summary table
    performance_data = []
    for name, result in results.items():
        performance_data.append({
            'Model': name,
            'AUC-ROC': result['auc_roc'],
            'Precision': result['precision'],
            'Recall': result['recall'],
            'F1-Score': result['f1_score']
        })

    performance_df = pd.DataFrame(performance_data)
    performance_df = performance_df.sort_values('AUC-ROC', ascending=False)

    print("\n📊 PERFORMANCE SUMMARY:")
    print(performance_df.to_string(index=False, float_format='%.4f'))

    # Find best performing model
    best_model_name = performance_df.iloc[0]['Model']
    best_auc = performance_df.iloc[0]['AUC-ROC']
    best_precision = performance_df.iloc[0]['Precision']
    best_recall = performance_df.iloc[0]['Recall']
    best_f1 = performance_df.iloc[0]['F1-Score']

    print(f"\n🏆 BEST PERFORMING MODEL: {best_model_name}")
    print(f"   • AUC-ROC: {best_auc:.4f}")
    print(f"   • Precision: {best_precision:.4f}")
    print(f"   • Recall: {best_recall:.4f}")
    print(f"   • F1-Score: {best_f1:.4f}")

    # Create visualizations
    create_performance_visualizations(results, y_test, performance_df)

    return performance_df

def create_performance_visualizations(results, y_test, performance_df):
    """
    Create comprehensive visualizations for model performance.

    Parameters:
    -----------
    results : dict
        Dictionary containing model results
    y_test : array
        True test labels
    performance_df : DataFrame
        Performance summary dataframe
    """
    # Set up the plotting style
    plt.style.use('default')
    fig = plt.figure(figsize=(20, 12))

    # 1. ROC Curves
    plt.subplot(2, 4, 1)
    for name, result in results.items():
        fpr, tpr, _ = roc_curve(y_test, result['y_pred_proba'])
        plt.plot(fpr, tpr, label=f"{name} (AUC = {result['auc_roc']:.3f})", linewidth=2)

    plt.plot([0, 1], [0, 1], 'k--', alpha=0.5)
    plt.xlabel('False Positive Rate')
    plt.ylabel('True Positive Rate')
    plt.title('ROC Curves Comparison (SMOTE)')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # 2. Precision-Recall Curves
    plt.subplot(2, 4, 2)
    for name, result in results.items():
        precision_curve, recall_curve, _ = precision_recall_curve(y_test, result['y_pred_proba'])
        plt.plot(recall_curve, precision_curve, label=f"{name}", linewidth=2)

    plt.xlabel('Recall')
    plt.ylabel('Precision')
    plt.title('Precision-Recall Curves (SMOTE)')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # 3. AUC-ROC Comparison Bar Chart
    plt.subplot(2, 4, 3)
    bars = plt.bar(performance_df['Model'], performance_df['AUC-ROC'],
                   color=['#1f77b4', '#ff7f0e', '#2ca02c'])
    plt.title('AUC-ROC Comparison (SMOTE)')
    plt.ylabel('AUC-ROC Score')
    plt.xticks(rotation=45)
    plt.ylim(0, 1)

    # Add value labels on bars
    for bar, value in zip(bars, performance_df['AUC-ROC']):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                f'{value:.3f}', ha='center', va='bottom')

    # 4. F1-Score Comparison Bar Chart
    plt.subplot(2, 4, 4)
    bars = plt.bar(performance_df['Model'], performance_df['F1-Score'],
                   color=['#1f77b4', '#ff7f0e', '#2ca02c'])
    plt.title('F1-Score Comparison (SMOTE)')
    plt.ylabel('F1-Score')
    plt.xticks(rotation=45)
    plt.ylim(0, 1)

    # Add value labels on bars
    for bar, value in zip(bars, performance_df['F1-Score']):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                f'{value:.3f}', ha='center', va='bottom')

    # 5-7. Confusion Matrices
    for i, (name, result) in enumerate(results.items()):
        plt.subplot(2, 4, 5 + i)
        cm = confusion_matrix(y_test, result['y_pred'])
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                   xticklabels=['Active', 'Discontinued'],
                   yticklabels=['Active', 'Discontinued'])
        plt.title(f'{name}\nConfusion Matrix (SMOTE)')
        plt.ylabel('True Label')
        plt.xlabel('Predicted Label')

    # 8. Performance Summary Table
    plt.subplot(2, 4, 8)
    plt.axis('off')

    # Create table text
    table_text = "SMOTE PERFORMANCE SUMMARY\n" + "="*30 + "\n\n"
    for _, row in performance_df.iterrows():
        table_text += f"{row['Model']}:\n"
        table_text += f"  AUC-ROC: {row['AUC-ROC']:.4f}\n"
        table_text += f"  Precision: {row['Precision']:.4f}\n"
        table_text += f"  Recall: {row['Recall']:.4f}\n"
        table_text += f"  F1-Score: {row['F1-Score']:.4f}\n\n"

    plt.text(0.1, 0.9, table_text, transform=plt.gca().transAxes,
             fontsize=9, verticalalignment='top', fontfamily='monospace')

    plt.tight_layout()
    plt.savefig('baseline_ensemble_models_smote_evaluation.png', dpi=300, bbox_inches='tight')
    plt.show()

    print("\n✅ Comprehensive SMOTE visualizations created and saved as 'baseline_ensemble_models_smote_evaluation.png'")

def analyze_feature_importance_with_shap_smote(results, X_test, y_test, feature_names, use_full_dataset=True):
    """
    Perform SHAP analysis for feature explainability across all SMOTE-balanced models.

    Parameters:
    -----------
    results : dict
        Dictionary containing trained models and results
    X_test : array
        Test features (scaled)
    y_test : array
        Test labels
    feature_names : list
        List of feature names
    use_full_dataset : bool
        Whether to use the entire test dataset for SHAP analysis (default: True)

    Returns:
    --------
    dict
        Dictionary containing SHAP values and explainers for each model
    """
    if not SHAP_AVAILABLE:
        print("❌ SHAP not available - cannot perform feature explainability analysis")
        print("📋 Install SHAP with: python -m pip install shap")
        return None

    print("\n" + "=" * 80)
    print("FEATURE EXPLAINABILITY ANALYSIS WITH SHAP (SMOTE-BALANCED MODELS)")
    print("=" * 80)

    # Use full test dataset for comprehensive SHAP analysis
    if use_full_dataset:
        X_sample = X_test
        y_sample = y_test
        print(f"📊 Using entire test dataset ({len(X_test):,} instances) for comprehensive SHAP analysis")
    else:
        # Fallback to sampling if needed for computational constraints
        sample_size = 500
        if len(X_test) > sample_size:
            sample_indices = np.random.choice(len(X_test), sample_size, replace=False)
            X_sample = X_test[sample_indices]
            y_sample = y_test[sample_indices] if hasattr(y_test, '__getitem__') else y_test.iloc[sample_indices]
            print(f"📊 Using random sample of {sample_size} instances for SHAP analysis")
        else:
            X_sample = X_test
            y_sample = y_test
            print(f"📊 Using all {len(X_test)} test instances for SHAP analysis")

    shap_results = {}

    for name, result in results.items():
        print(f"\n🔄 Computing SHAP values for {name} (SMOTE-balanced)...")

        try:
            model = result['model']

            # Create TreeExplainer for tree-based models
            explainer = shap.TreeExplainer(model)
            shap_values = explainer.shap_values(X_sample)

            # For binary classification, get SHAP values for positive class
            if isinstance(shap_values, list) and len(shap_values) == 2:
                shap_values = shap_values[1]  # Positive class (discontinued)
            elif isinstance(shap_values, np.ndarray) and shap_values.ndim == 3:
                shap_values = shap_values[:, :, 1]  # Positive class (discontinued)
            elif isinstance(shap_values, np.ndarray) and shap_values.shape[-1] == 2:
                shap_values = shap_values[:, 1]  # Positive class (discontinued)

            shap_results[name] = {
                'explainer': explainer,
                'shap_values': shap_values,
                'X_sample': X_sample,
                'y_sample': y_sample
            }

            print(f"✓ {name} SHAP analysis completed")

        except Exception as e:
            print(f"❌ Error computing SHAP values for {name}: {e}")
            continue

    if shap_results:
        # Create SHAP visualizations
        create_shap_visualizations_smote(shap_results, feature_names)

        # Generate feature importance summary
        generate_feature_importance_summary_smote(shap_results, feature_names)

    return shap_results

def create_shap_visualizations_smote(shap_results, feature_names):
    """
    Create comprehensive SHAP visualizations for SMOTE-balanced models.

    Parameters:
    -----------
    shap_results : dict
        Dictionary containing SHAP values and explainers
    feature_names : list
        List of feature names
    """
    print(f"\n📊 Creating SHAP visualizations for SMOTE-balanced models...")

    # 1. Feature Importance Plots (Mean Absolute SHAP Values)
    fig, axes = plt.subplots(1, len(shap_results), figsize=(6*len(shap_results), 8))
    if len(shap_results) == 1:
        axes = [axes]

    for i, (name, shap_data) in enumerate(shap_results.items()):
        shap_values = shap_data['shap_values']

        # Calculate mean absolute SHAP values
        mean_shap = np.abs(shap_values).mean(axis=0)

        # Ensure mean_shap is 1-dimensional
        if mean_shap.ndim > 1:
            mean_shap = mean_shap.flatten()

        # Ensure we have the right number of features
        if len(mean_shap) != len(feature_names):
            min_len = min(len(mean_shap), len(feature_names))
            mean_shap = mean_shap[:min_len]
            current_features = feature_names[:min_len]
        else:
            current_features = feature_names

        feature_importance = pd.DataFrame({
            'feature': current_features,
            'importance': mean_shap
        }).sort_values('importance', ascending=True)

        # Plot top 15 features
        top_features = feature_importance.tail(15)
        axes[i].barh(range(len(top_features)), top_features['importance'])
        axes[i].set_yticks(range(len(top_features)))
        axes[i].set_yticklabels(top_features['feature'])
        axes[i].set_xlabel('Mean |SHAP Value|')
        axes[i].set_title(f'{name} (SMOTE)\nFeature Importance')
        axes[i].grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig('shap_feature_importance_smote_comparison.png', dpi=300, bbox_inches='tight')
    plt.show()

    # 2. SHAP Summary Plots for each model
    for name, shap_data in shap_results.items():
        try:
            plt.figure(figsize=(10, 8))
            shap.summary_plot(shap_data['shap_values'], shap_data['X_sample'],
                             feature_names=feature_names, show=False, max_display=15)
            plt.title(f'{name} (SMOTE) - SHAP Summary Plot')
            plt.tight_layout()
            plt.savefig(f'shap_summary_smote_{name.lower().replace(" ", "_")}.png', dpi=300, bbox_inches='tight')
            plt.show()
        except Exception as e:
            print(f"⚠️  Could not create summary plot for {name}: {e}")
            continue

    print("✅ SHAP visualizations for SMOTE models created and saved")

def generate_feature_importance_summary_smote(shap_results, feature_names):
    """
    Generate a comprehensive summary of feature importance across SMOTE-balanced models.

    Parameters:
    -----------
    shap_results : dict
        Dictionary containing SHAP values and explainers
    feature_names : list
        List of feature names
    """
    print(f"\n📋 FEATURE IMPORTANCE SUMMARY (SMOTE-BALANCED MODELS):")
    print("=" * 70)

    # Calculate feature importance for each model
    importance_data = {}

    for name, shap_data in shap_results.items():
        shap_values = shap_data['shap_values']
        mean_shap = np.abs(shap_values).mean(axis=0)

        # Ensure mean_shap is 1-dimensional
        if mean_shap.ndim > 1:
            mean_shap = mean_shap.flatten()

        # Ensure we have the right number of features
        if len(mean_shap) != len(feature_names):
            min_len = min(len(mean_shap), len(feature_names))
            mean_shap = mean_shap[:min_len]

        importance_data[name] = mean_shap

    # Create combined importance DataFrame
    min_length = min(len(feature_names), min(len(v) for v in importance_data.values()))

    # Truncate all arrays to the minimum length
    truncated_data = {}
    for name, values in importance_data.items():
        truncated_data[name] = values[:min_length]

    truncated_features = feature_names[:min_length]

    importance_df = pd.DataFrame(truncated_data, index=truncated_features)
    importance_df['Mean_Across_Models'] = importance_df.mean(axis=1)
    importance_df = importance_df.sort_values('Mean_Across_Models', ascending=False)

    print("\n🏆 TOP 10 MOST IMPORTANT FEATURES (SMOTE Models - Mean |SHAP Value|):")
    print("-" * 70)
    top_10 = importance_df.head(10)
    for i, (feature, row) in enumerate(top_10.iterrows(), 1):
        print(f"{i:2d}. {feature:<30} {row['Mean_Across_Models']:.4f}")
        for model_name in shap_results.keys():
            print(f"    {model_name:<20}: {row[model_name]:.4f}")
        print()

    # Save detailed importance to CSV
    importance_df.to_csv('shap_feature_importance_smote_detailed.csv')
    print("✅ Detailed SMOTE feature importance saved to 'shap_feature_importance_smote_detailed.csv'")

    return importance_df

def main():
    """
    Main function to run the complete SMOTE-balanced ensemble modeling pipeline.
    """
    # Load and preprocess data
    data_result = load_and_preprocess_data()

    if data_result[0] is None:
        print("❌ Pipeline terminated due to data loading issues.")
        return

    X_train, X_test, y_train, y_test, feature_names, df = data_result

    # Apply scaling and SMOTE
    X_train_balanced, X_test_scaled, y_train_balanced, scaler = apply_scaling_and_smote(
        X_train, X_test, y_train
    )

    # Initialize models
    models = build_baseline_models()

    # Train and evaluate models
    results = train_and_evaluate_models(models, X_train_balanced, X_test_scaled, y_train_balanced, y_test)

    # Create performance comparison
    performance_df = create_performance_comparison(results, y_test)

    # Perform SHAP analysis for feature explainability
    shap_results = analyze_feature_importance_with_shap_smote(
        results, X_test_scaled, y_test, feature_names
    )

    # Generate final summary
    print("\n" + "=" * 80)
    print("SMOTE-BALANCED ENSEMBLE MODELING COMPLETE")
    print("=" * 80)
    print("✅ Successfully trained and evaluated 3 SMOTE-balanced ensemble models")
    print("✅ Performance comparison completed")
    print("✅ Visualizations saved as 'baseline_ensemble_models_smote_evaluation.png'")
    if shap_results:
        print("✅ SHAP feature explainability analysis completed")
        print("✅ SHAP visualizations and importance rankings saved")
    print("\n📋 NEXT STEPS:")
    print("   1. Compare SMOTE results with original baseline models")
    print("   2. Analyze how class balancing affects feature importance")
    print("   3. Evaluate recall improvements for minority class detection")
    print("   4. Consider ensemble of original and SMOTE models for production")

    # Performance comparison with original models
    print("\n" + "=" * 80)
    print("COMPARISON WITH ORIGINAL BASELINE MODELS")
    print("=" * 80)
    print("📊 SMOTE Benefits Analysis:")
    print("   • Improved Recall: SMOTE typically increases recall for minority class")
    print("   • Balanced Training: 50-50 class distribution reduces model bias")
    print("   • Feature Scaling: StandardScaler ensures all features contribute equally")
    print("   • Synthetic Samples: SMOTE creates realistic minority class examples")
    print("\n📋 Key Metrics to Compare:")
    print("   • Recall (Sensitivity): Ability to identify discontinued patients")
    print("   • F1-Score: Harmonic mean of precision and recall")
    print("   • AUC-ROC: Overall discriminative ability")
    print("   • Feature Importance: How SMOTE affects SHAP rankings")

    return results, performance_df, shap_results, scaler

if __name__ == "__main__":
    results, performance_df, shap_results, scaler = main()
