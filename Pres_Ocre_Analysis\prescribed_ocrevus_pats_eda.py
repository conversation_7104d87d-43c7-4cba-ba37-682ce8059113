#!/usr/bin/env python3
"""
OCREVUS THERAPY: PHYSICIAN PRESCRIPTION VOLUME vs PATIENT DISCONTINUATION ANALYSIS
==================================================================================

This script performs comprehensive exploratory data analysis (EDA) on the relationship between
physician Ocrevus prescription volume ('prescribed_ocrevus_pats') and patient discontinuation
rates ('patient_status').

Hypothesis: Higher prescription counts by physicians are associated with lower patient
discontinuation rates (i.e., patients are more likely to remain active on therapy when
treated by physicians with more Ocrevus experience).

Author: Augment Agent
Date: 2025
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from scipy.stats import chi2_contingency, mannw<PERSON><PERSON><PERSON>, pearsonr, spearmanr
import warnings
warnings.filterwarnings('ignore')

# Set style for better visualizations
plt.style.use('default')
sns.set_palette("husl")
plt.rcParams['figure.figsize'] = (12, 8)
plt.rcParams['font.size'] = 10

def load_and_preprocess_data(file_path='Ocrevus_switch_alerts.csv'):
    """
    Load the dataset and perform initial preprocessing for physician prescription volume analysis.

    Parameters:
    -----------
    file_path : str
        Path to the CSV file containing the Ocrevus data

    Returns:
    --------
    pd.DataFrame
        Preprocessed dataframe with clean prescribed_ocrevus_pats and patient_status columns
    """
    print("=" * 80)
    print("OCREVUS THERAPY: PHYSICIAN PRESCRIPTION VOLUME vs DISCONTINUATION ANALYSIS")
    print("=" * 80)

    # Load the dataset
    try:
        df = pd.read_csv(file_path)
        print(f"✓ Dataset loaded successfully: {df.shape[0]:,} rows × {df.shape[1]} columns")
    except FileNotFoundError:
        print(f"❌ Error: File '{file_path}' not found.")
        return None
    except Exception as e:
        print(f"❌ Error loading data: {e}")
        return None

    # Check for required columns
    required_cols = ['prescribed_ocrevus_pats', 'patientsubstatus']
    missing_cols = [col for col in required_cols if col not in df.columns]
    if missing_cols:
        print(f"❌ Error: Missing required columns: {missing_cols}")
        return None

    print(f"✓ Required columns found: {required_cols}")

    # 1. Handle missing values in prescribed_ocrevus_pats
    missing_prescriptions = df['prescribed_ocrevus_pats'].isna().sum()
    print(f"\n🔍 Data Quality Assessment:")
    print(f"   • Missing values in prescribed_ocrevus_pats: {missing_prescriptions:,}")

    if missing_prescriptions > 0:
        print(f"⚠️  Warning: {missing_prescriptions} missing values in prescribed_ocrevus_pats")
        # Remove rows with missing prescribed_ocrevus_pats as per requirements
        df = df.dropna(subset=['prescribed_ocrevus_pats'])
        print(f"✓ Removed rows with missing prescribed_ocrevus_pats. New shape: {df.shape}")
    else:
        print("✓ No missing values in prescribed_ocrevus_pats")

    # 2. Create binary patient_status from patientsubstatus
    print(f"\n🏷️  Creating binary patient status...")
    print(f"Original patientsubstatus categories: {df['patientsubstatus'].nunique()}")
    print("Sample categories:", df['patientsubstatus'].unique()[:5])

    # Create simplified status: Active vs Discontinued
    df['patient_status'] = df['patientsubstatus'].apply(
        lambda x: 'Discontinued' if 'Discontinued' in str(x) else 'Active'
    )

    print(f"✓ Binary patient status created:")
    status_counts = df['patient_status'].value_counts()
    for status, count in status_counts.items():
        percentage = (count / len(df)) * 100
        print(f"   • {status}: {count:,} patients ({percentage:.1f}%)")

    # 3. Data type validation and conversion
    print(f"\n🔧 Data Type Validation:")
    print(f"   • prescribed_ocrevus_pats dtype: {df['prescribed_ocrevus_pats'].dtype}")

    # Ensure prescribed_ocrevus_pats is numeric
    if not pd.api.types.is_numeric_dtype(df['prescribed_ocrevus_pats']):
        print("⚠️  Converting prescribed_ocrevus_pats to numeric...")
        df['prescribed_ocrevus_pats'] = pd.to_numeric(df['prescribed_ocrevus_pats'], errors='coerce')
        # Remove any rows that couldn't be converted
        df = df.dropna(subset=['prescribed_ocrevus_pats'])
        print(f"✓ Conversion complete. Final shape: {df.shape}")

    # 4. Final data validation
    print(f"\n📊 Final dataset summary:")
    print(f"   • Total patients: {len(df):,}")
    print(f"   • Prescription volume range: {df['prescribed_ocrevus_pats'].min():.0f} - {df['prescribed_ocrevus_pats'].max():.0f}")
    print(f"   • Active patients: {len(df[df['patient_status'] == 'Active']):,}")
    print(f"   • Discontinued patients: {len(df[df['patient_status'] == 'Discontinued']):,}")

    return df

def generate_descriptive_statistics(df):
    """
    Generate comprehensive descriptive statistics for prescribed_ocrevus_pats.

    Parameters:
    -----------
    df : pd.DataFrame
        The preprocessed dataframe

    Returns:
    --------
    dict
        Dictionary containing descriptive statistics
    """
    print("\n" + "=" * 60)
    print("DESCRIPTIVE STATISTICS: PHYSICIAN PRESCRIPTION VOLUME")
    print("=" * 60)

    # Overall statistics
    stats_overall = df['prescribed_ocrevus_pats'].describe()

    print("📊 OVERALL PRESCRIPTION VOLUME STATISTICS:")
    print(f"   • Count:        {stats_overall['count']:>10.0f}")
    print(f"   • Mean:         {stats_overall['mean']:>10.2f}")
    print(f"   • Median:       {stats_overall['50%']:>10.2f}")
    print(f"   • Std Dev:      {stats_overall['std']:>10.2f}")
    print(f"   • Min:          {stats_overall['min']:>10.0f}")
    print(f"   • Max:          {stats_overall['max']:>10.0f}")
    print(f"   • Q1 (25%):     {stats_overall['25%']:>10.2f}")
    print(f"   • Q3 (75%):     {stats_overall['75%']:>10.2f}")

    # Additional statistics
    mode_val = df['prescribed_ocrevus_pats'].mode().iloc[0] if not df['prescribed_ocrevus_pats'].mode().empty else 'N/A'
    skewness = df['prescribed_ocrevus_pats'].skew()
    kurtosis = df['prescribed_ocrevus_pats'].kurtosis()

    print(f"   • Mode:         {mode_val:>10}")
    print(f"   • Skewness:     {skewness:>10.3f}")
    print(f"   • Kurtosis:     {kurtosis:>10.3f}")

    # Outlier detection using IQR method
    Q1 = stats_overall['25%']
    Q3 = stats_overall['75%']
    IQR = Q3 - Q1
    lower_bound = Q1 - 1.5 * IQR
    upper_bound = Q3 + 1.5 * IQR

    outliers = df[(df['prescribed_ocrevus_pats'] < lower_bound) |
                  (df['prescribed_ocrevus_pats'] > upper_bound)]

    print(f"\n🔍 OUTLIER ANALYSIS (IQR Method):")
    print(f"   • IQR:          {IQR:>10.2f}")
    print(f"   • Lower bound:  {lower_bound:>10.2f}")
    print(f"   • Upper bound:  {upper_bound:>10.2f}")
    print(f"   • Outliers:     {len(outliers):>10,} ({len(outliers)/len(df)*100:.1f}%)")

    # Distribution characteristics
    print(f"\n📈 DISTRIBUTION CHARACTERISTICS:")
    if skewness > 1:
        skew_desc = "Highly right-skewed"
    elif skewness > 0.5:
        skew_desc = "Moderately right-skewed"
    elif skewness > -0.5:
        skew_desc = "Approximately symmetric"
    elif skewness > -1:
        skew_desc = "Moderately left-skewed"
    else:
        skew_desc = "Highly left-skewed"

    print(f"   • Distribution shape: {skew_desc}")
    print(f"   • Tail behavior: {'Heavy tails' if abs(kurtosis) > 3 else 'Normal tails'}")

    return {
        'overall_stats': stats_overall,
        'mode': mode_val,
        'skewness': skewness,
        'kurtosis': kurtosis,
        'outliers_count': len(outliers),
        'outliers_percentage': len(outliers)/len(df)*100
    }

def analyze_by_patient_status(df):
    """
    Analyze prescription volume by patient status (Active vs Discontinued).

    Parameters:
    -----------
    df : pd.DataFrame
        The preprocessed dataframe

    Returns:
    --------
    dict
        Statistical test results
    """
    print("\n" + "-" * 60)
    print("ANALYSIS BY PATIENT STATUS")
    print("-" * 60)

    # Group by patient status
    active_prescriptions = df[df['patient_status'] == 'Active']['prescribed_ocrevus_pats']
    discontinued_prescriptions = df[df['patient_status'] == 'Discontinued']['prescribed_ocrevus_pats']

    print("📊 DESCRIPTIVE STATISTICS BY GROUP:")
    print("\n🟢 ACTIVE PATIENTS:")
    active_stats = active_prescriptions.describe()
    print(f"   • Count:    {active_stats['count']:>8.0f}")
    print(f"   • Mean:     {active_stats['mean']:>8.2f}")
    print(f"   • Median:   {active_stats['50%']:>8.2f}")
    print(f"   • Std Dev:  {active_stats['std']:>8.2f}")
    print(f"   • Min:      {active_stats['min']:>8.1f}")
    print(f"   • Max:      {active_stats['max']:>8.1f}")

    print("\n🔴 DISCONTINUED PATIENTS:")
    discontinued_stats = discontinued_prescriptions.describe()
    print(f"   • Count:    {discontinued_stats['count']:>8.0f}")
    print(f"   • Mean:     {discontinued_stats['mean']:>8.2f}")
    print(f"   • Median:   {discontinued_stats['50%']:>8.2f}")
    print(f"   • Std Dev:  {discontinued_stats['std']:>8.2f}")
    print(f"   • Min:      {discontinued_stats['min']:>8.1f}")
    print(f"   • Max:      {discontinued_stats['max']:>8.1f}")

    # Calculate differences
    mean_diff = active_stats['mean'] - discontinued_stats['mean']
    median_diff = active_stats['50%'] - discontinued_stats['50%']

    print(f"\n📈 KEY DIFFERENCES:")
    print(f"   • Mean difference (Active - Discontinued):   {mean_diff:>8.2f}")
    print(f"   • Median difference (Active - Discontinued): {median_diff:>8.2f}")
    print(f"   • Relative mean difference:                  {(mean_diff/discontinued_stats['mean'])*100:>8.1f}%")

    return {
        'active_stats': active_stats,
        'discontinued_stats': discontinued_stats,
        'mean_difference': mean_diff,
        'median_difference': median_diff
    }

def perform_statistical_tests(df):
    """
    Perform statistical tests to assess the relationship between prescription volume and patient status.

    Parameters:
    -----------
    df : pd.DataFrame
        The preprocessed dataframe

    Returns:
    --------
    dict
        Dictionary containing statistical test results
    """
    print("\n" + "=" * 60)
    print("STATISTICAL SIGNIFICANCE TESTING")
    print("=" * 60)

    # Separate data by patient status
    active_prescriptions = df[df['patient_status'] == 'Active']['prescribed_ocrevus_pats']
    discontinued_prescriptions = df[df['patient_status'] == 'Discontinued']['prescribed_ocrevus_pats']

    # 1. Mann-Whitney U Test (non-parametric alternative to t-test)
    print("🧪 MANN-WHITNEY U TEST:")
    print("   Null Hypothesis: No difference in prescription volumes between groups")

    try:
        statistic, p_value = mannwhitneyu(active_prescriptions, discontinued_prescriptions,
                                         alternative='two-sided')

        print(f"   • Test statistic: {statistic:,.2f}")
        print(f"   • P-value:        {p_value:.6f}")

        alpha = 0.05
        if p_value < alpha:
            print(f"   • Result:         SIGNIFICANT (p < {alpha})")
            print(f"   • Interpretation: There IS a significant difference in prescription volumes")
        else:
            print(f"   • Result:         NOT SIGNIFICANT (p >= {alpha})")
            print(f"   • Interpretation: No significant difference in prescription volumes")

        # Effect size (r = Z / sqrt(N))
        n1, n2 = len(active_prescriptions), len(discontinued_prescriptions)
        z_score = stats.norm.ppf(1 - p_value/2)  # Approximate Z-score
        effect_size = z_score / np.sqrt(n1 + n2)

        print(f"   • Effect size (r): {effect_size:.3f}")
        if abs(effect_size) < 0.1:
            effect_desc = "negligible"
        elif abs(effect_size) < 0.3:
            effect_desc = "small"
        elif abs(effect_size) < 0.5:
            effect_desc = "medium"
        else:
            effect_desc = "large"
        print(f"   • Effect magnitude: {effect_desc}")

    except Exception as e:
        print(f"   • Error in Mann-Whitney U test: {e}")
        statistic, p_value, effect_size = None, None, None

    # 2. Correlation Analysis
    print(f"\n🔗 CORRELATION ANALYSIS:")

    # Create binary encoding for correlation (0 = Active, 1 = Discontinued)
    df_corr = df.copy()
    df_corr['status_binary'] = (df_corr['patient_status'] == 'Discontinued').astype(int)

    # Pearson correlation
    try:
        pearson_r, pearson_p = pearsonr(df_corr['prescribed_ocrevus_pats'], df_corr['status_binary'])
        print(f"   • Pearson correlation:  r = {pearson_r:.4f}, p = {pearson_p:.6f}")
    except Exception as e:
        print(f"   • Error in Pearson correlation: {e}")
        pearson_r, pearson_p = None, None

    # Spearman correlation (rank-based, more robust)
    try:
        spearman_r, spearman_p = spearmanr(df_corr['prescribed_ocrevus_pats'], df_corr['status_binary'])
        print(f"   • Spearman correlation: ρ = {spearman_r:.4f}, p = {spearman_p:.6f}")
    except Exception as e:
        print(f"   • Error in Spearman correlation: {e}")
        spearman_r, spearman_p = None, None

    # Interpretation
    print(f"\n📊 CORRELATION INTERPRETATION:")
    if pearson_r is not None:
        if pearson_r > 0:
            direction = "POSITIVE (higher prescription volume → higher discontinuation rate)"
        elif pearson_r < 0:
            direction = "NEGATIVE (higher prescription volume → lower discontinuation rate)"
        else:
            direction = "NO CORRELATION"

        strength = "negligible" if abs(pearson_r) < 0.1 else \
                  "weak" if abs(pearson_r) < 0.3 else \
                  "moderate" if abs(pearson_r) < 0.5 else \
                  "strong" if abs(pearson_r) < 0.7 else "very strong"

        print(f"   • Direction: {direction}")
        print(f"   • Strength:  {strength.upper()}")

        # Hypothesis validation
        print(f"\n🎯 HYPOTHESIS VALIDATION:")
        if pearson_r < 0 and pearson_p < 0.05:
            print("   ✅ HYPOTHESIS SUPPORTED: Higher prescription volumes are associated with lower discontinuation rates")
        elif pearson_r > 0 and pearson_p < 0.05:
            print("   ❌ HYPOTHESIS CONTRADICTED: Higher prescription volumes are associated with higher discontinuation rates")
        else:
            print("   ⚠️  HYPOTHESIS INCONCLUSIVE: No significant relationship found")

    return {
        'mannwhitney_statistic': statistic,
        'mannwhitney_pvalue': p_value,
        'mannwhitney_effect_size': effect_size,
        'pearson_r': pearson_r,
        'pearson_p': pearson_p,
        'spearman_r': spearman_r,
        'spearman_p': spearman_p
    }

def create_distribution_visualizations(df):
    """
    Create comprehensive visualizations for prescription volume distribution.

    Parameters:
    -----------
    df : pd.DataFrame
        The preprocessed dataframe
    """
    print("\n" + "=" * 60)
    print("CREATING DISTRIBUTION VISUALIZATIONS")
    print("=" * 60)

    # Create a figure with multiple subplots
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('Physician Prescription Volume Distribution Analysis', fontsize=16, fontweight='bold')

    # 1. Overall distribution histogram
    axes[0, 0].hist(df['prescribed_ocrevus_pats'], bins=50, alpha=0.7, color='skyblue', edgecolor='black')
    axes[0, 0].axvline(df['prescribed_ocrevus_pats'].mean(), color='red', linestyle='--',
                       label=f'Mean: {df["prescribed_ocrevus_pats"].mean():.1f}')
    axes[0, 0].axvline(df['prescribed_ocrevus_pats'].median(), color='orange', linestyle='--',
                       label=f'Median: {df["prescribed_ocrevus_pats"].median():.1f}')
    axes[0, 0].set_title('Overall Distribution of Prescription Volume')
    axes[0, 0].set_xlabel('Prescribed Ocrevus Patients Count')
    axes[0, 0].set_ylabel('Frequency')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)

    # 2. Box plot by patient status
    df.boxplot(column='prescribed_ocrevus_pats', by='patient_status', ax=axes[0, 1])
    axes[0, 1].set_title('Prescription Volume by Patient Status')
    axes[0, 1].set_xlabel('Patient Status')
    axes[0, 1].set_ylabel('Prescribed Ocrevus Patients Count')
    axes[0, 1].grid(True, alpha=0.3)

    # 3. Overlapping histograms by status
    active_data = df[df['patient_status'] == 'Active']['prescribed_ocrevus_pats']
    discontinued_data = df[df['patient_status'] == 'Discontinued']['prescribed_ocrevus_pats']

    axes[1, 0].hist(active_data, bins=30, alpha=0.6, label='Active', color='green', density=True)
    axes[1, 0].hist(discontinued_data, bins=30, alpha=0.6, label='Discontinued', color='red', density=True)
    axes[1, 0].set_title('Distribution Comparison by Patient Status')
    axes[1, 0].set_xlabel('Prescribed Ocrevus Patients Count')
    axes[1, 0].set_ylabel('Density')
    axes[1, 0].legend()
    axes[1, 0].grid(True, alpha=0.3)

    # 4. Violin plot for detailed distribution comparison
    data_for_violin = [active_data, discontinued_data]
    parts = axes[1, 1].violinplot(data_for_violin, positions=[1, 2], showmeans=True, showmedians=True)
    axes[1, 1].set_title('Detailed Distribution Comparison (Violin Plot)')
    axes[1, 1].set_xlabel('Patient Status')
    axes[1, 1].set_ylabel('Prescribed Ocrevus Patients Count')
    axes[1, 1].set_xticks([1, 2])
    axes[1, 1].set_xticklabels(['Active', 'Discontinued'])
    axes[1, 1].grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig('prescribed_ocrevus_pats_distribution_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()

    print("✅ Distribution visualizations created and saved as 'prescribed_ocrevus_pats_distribution_analysis.png'")

def perform_segmentation_analysis(df):
    """
    Perform segmentation analysis by creating prescription volume bins and analyzing discontinuation rates.

    Parameters:
    -----------
    df : pd.DataFrame
        The preprocessed dataframe

    Returns:
    --------
    dict
        Dictionary containing segmentation analysis results
    """
    print("\n" + "=" * 60)
    print("SEGMENTATION ANALYSIS: PRESCRIPTION VOLUME BINS")
    print("=" * 60)

    # Create prescription volume bins
    # Method 1: Quartile-based bins
    quartiles = df['prescribed_ocrevus_pats'].quantile([0.25, 0.5, 0.75]).values

    def categorize_prescription_volume(volume):
        if volume <= quartiles[0]:
            return 'Low (Q1)'
        elif volume <= quartiles[1]:
            return 'Medium-Low (Q2)'
        elif volume <= quartiles[2]:
            return 'Medium-High (Q3)'
        else:
            return 'High (Q4)'

    df['prescription_volume_category'] = df['prescribed_ocrevus_pats'].apply(categorize_prescription_volume)

    print("📊 PRESCRIPTION VOLUME CATEGORIES (Quartile-based):")
    print(f"   • Low (Q1):        ≤ {quartiles[0]:.0f} patients")
    print(f"   • Medium-Low (Q2): {quartiles[0]:.0f} - {quartiles[1]:.0f} patients")
    print(f"   • Medium-High (Q3): {quartiles[1]:.0f} - {quartiles[2]:.0f} patients")
    print(f"   • High (Q4):       > {quartiles[2]:.0f} patients")

    # Calculate discontinuation rates by category
    discontinuation_by_category = df.groupby('prescription_volume_category').agg({
        'patient_status': ['count', lambda x: (x == 'Discontinued').sum()],
        'prescribed_ocrevus_pats': ['mean', 'median', 'std']
    }).round(2)

    # Flatten column names
    discontinuation_by_category.columns = ['total_patients', 'discontinued_patients',
                                         'mean_prescriptions', 'median_prescriptions', 'std_prescriptions']

    # Calculate discontinuation rates
    discontinuation_by_category['discontinuation_rate'] = (
        discontinuation_by_category['discontinued_patients'] /
        discontinuation_by_category['total_patients'] * 100
    ).round(2)

    # Calculate active patients
    discontinuation_by_category['active_patients'] = (
        discontinuation_by_category['total_patients'] -
        discontinuation_by_category['discontinued_patients']
    )

    print(f"\n📈 DISCONTINUATION RATES BY PRESCRIPTION VOLUME CATEGORY:")
    print(discontinuation_by_category[['total_patients', 'active_patients', 'discontinued_patients', 'discontinuation_rate']])

    print(f"\n📊 PRESCRIPTION STATISTICS BY CATEGORY:")
    print(discontinuation_by_category[['mean_prescriptions', 'median_prescriptions', 'std_prescriptions']])

    # Test for trend in discontinuation rates
    print(f"\n🧪 TREND ANALYSIS:")
    categories_ordered = ['Low (Q1)', 'Medium-Low (Q2)', 'Medium-High (Q3)', 'High (Q4)']
    rates = [discontinuation_by_category.loc[cat, 'discontinuation_rate'] for cat in categories_ordered]

    # Calculate trend direction
    trend_changes = [rates[i+1] - rates[i] for i in range(len(rates)-1)]
    avg_change = np.mean(trend_changes)

    print(f"   • Discontinuation rates by category: {rates}")
    print(f"   • Average change per category: {avg_change:.2f} percentage points")

    if avg_change < -1:
        trend_desc = "DECREASING (supports hypothesis)"
    elif avg_change > 1:
        trend_desc = "INCREASING (contradicts hypothesis)"
    else:
        trend_desc = "STABLE (no clear trend)"

    print(f"   • Overall trend: {trend_desc}")

    # Chi-square test for independence
    print(f"\n🧪 CHI-SQUARE TEST FOR INDEPENDENCE:")
    contingency_table = pd.crosstab(df['prescription_volume_category'], df['patient_status'])
    print("Contingency Table:")
    print(contingency_table)

    try:
        chi2, p_value, dof, expected = chi2_contingency(contingency_table)
        print(f"\n   • Chi-square statistic: {chi2:.4f}")
        print(f"   • Degrees of freedom: {dof}")
        print(f"   • P-value: {p_value:.6f}")

        if p_value < 0.05:
            print(f"   • Result: SIGNIFICANT association between prescription volume and patient status")
        else:
            print(f"   • Result: NO significant association between prescription volume and patient status")

    except Exception as e:
        print(f"   • Error in Chi-square test: {e}")
        chi2, p_value = None, None

    return {
        'discontinuation_by_category': discontinuation_by_category,
        'trend_direction': avg_change,
        'chi2_statistic': chi2,
        'chi2_pvalue': p_value,
        'quartiles': quartiles
    }

def create_relationship_visualizations(df):
    """
    Create visualizations showing the relationship between prescription volume and patient status.

    Parameters:
    -----------
    df : pd.DataFrame
        The preprocessed dataframe
    """
    print("\n" + "=" * 60)
    print("CREATING RELATIONSHIP VISUALIZATIONS")
    print("=" * 60)

    # Create a figure with multiple subplots
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('Prescription Volume vs Patient Status Relationship Analysis', fontsize=16, fontweight='bold')

    # 1. Discontinuation rate by prescription volume category
    if 'prescription_volume_category' in df.columns:
        category_rates = df.groupby('prescription_volume_category').apply(
            lambda x: (x['patient_status'] == 'Discontinued').mean() * 100
        ).reindex(['Low (Q1)', 'Medium-Low (Q2)', 'Medium-High (Q3)', 'High (Q4)'])

        bars = axes[0, 0].bar(range(len(category_rates)), category_rates.values,
                             color=['red', 'orange', 'yellow', 'green'], alpha=0.7)
        axes[0, 0].set_title('Discontinuation Rate by Prescription Volume Category')
        axes[0, 0].set_xlabel('Prescription Volume Category')
        axes[0, 0].set_ylabel('Discontinuation Rate (%)')
        axes[0, 0].set_xticks(range(len(category_rates)))
        axes[0, 0].set_xticklabels(category_rates.index, rotation=45)
        axes[0, 0].grid(True, alpha=0.3)

        # Add value labels on bars
        for bar, value in zip(bars, category_rates.values):
            axes[0, 0].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5,
                           f'{value:.1f}%', ha='center', va='bottom')

    # 2. Scatter plot with trend line
    active_mask = df['patient_status'] == 'Active'
    discontinued_mask = df['patient_status'] == 'Discontinued'

    axes[0, 1].scatter(df[active_mask]['prescribed_ocrevus_pats'],
                      np.random.normal(0, 0.1, sum(active_mask)),
                      alpha=0.6, color='green', label='Active', s=20)
    axes[0, 1].scatter(df[discontinued_mask]['prescribed_ocrevus_pats'],
                      np.random.normal(1, 0.1, sum(discontinued_mask)),
                      alpha=0.6, color='red', label='Discontinued', s=20)

    # Add trend line
    x_vals = df['prescribed_ocrevus_pats']
    y_vals = (df['patient_status'] == 'Discontinued').astype(int)
    z = np.polyfit(x_vals, y_vals, 1)
    p = np.poly1d(z)
    axes[0, 1].plot(sorted(x_vals), p(sorted(x_vals)), "b--", alpha=0.8, linewidth=2)

    axes[0, 1].set_title('Patient Status by Prescription Volume (with Trend)')
    axes[0, 1].set_xlabel('Prescribed Ocrevus Patients Count')
    axes[0, 1].set_ylabel('Patient Status (0=Active, 1=Discontinued)')
    axes[0, 1].set_yticks([0, 1])
    axes[0, 1].set_yticklabels(['Active', 'Discontinued'])
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)

    # 3. Count plot by category
    if 'prescription_volume_category' in df.columns:
        category_order = ['Low (Q1)', 'Medium-Low (Q2)', 'Medium-High (Q3)', 'High (Q4)']
        sns.countplot(data=df, x='prescription_volume_category', hue='patient_status',
                     order=category_order, ax=axes[1, 0])
        axes[1, 0].set_title('Patient Count by Prescription Volume Category')
        axes[1, 0].set_xlabel('Prescription Volume Category')
        axes[1, 0].set_ylabel('Patient Count')
        axes[1, 0].tick_params(axis='x', rotation=45)
        axes[1, 0].grid(True, alpha=0.3)

    # 4. Proportion plot
    if 'prescription_volume_category' in df.columns:
        prop_data = df.groupby(['prescription_volume_category', 'patient_status']).size().unstack()
        prop_data_pct = prop_data.div(prop_data.sum(axis=1), axis=0) * 100
        prop_data_pct = prop_data_pct.reindex(category_order)

        prop_data_pct.plot(kind='bar', stacked=True, ax=axes[1, 1],
                          color=['green', 'red'], alpha=0.7)
        axes[1, 1].set_title('Patient Status Proportion by Prescription Volume Category')
        axes[1, 1].set_xlabel('Prescription Volume Category')
        axes[1, 1].set_ylabel('Percentage (%)')
        axes[1, 1].tick_params(axis='x', rotation=45)
        axes[1, 1].legend(title='Patient Status')
        axes[1, 1].grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig('prescribed_ocrevus_pats_relationship_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()

    print("✅ Relationship visualizations created and saved as 'prescribed_ocrevus_pats_relationship_analysis.png'")

def generate_comprehensive_summary(df, desc_stats, status_analysis, test_results, segmentation_results):
    """
    Generate a comprehensive summary report of all findings.

    Parameters:
    -----------
    df : pd.DataFrame
        The preprocessed dataframe
    desc_stats : dict
        Descriptive statistics results
    status_analysis : dict
        Patient status analysis results
    test_results : dict
        Statistical test results
    segmentation_results : dict
        Segmentation analysis results
    """
    print("\n" + "=" * 80)
    print("COMPREHENSIVE SUMMARY REPORT")
    print("=" * 80)

    # Dataset overview
    total_patients = len(df)
    active_patients = len(df[df['patient_status'] == 'Active'])
    discontinued_patients = len(df[df['patient_status'] == 'Discontinued'])
    overall_discontinuation_rate = (discontinued_patients / total_patients) * 100

    print("📊 DATASET OVERVIEW:")
    print(f"   • Total patients analyzed: {total_patients:,}")
    print(f"   • Active patients: {active_patients:,} ({(active_patients/total_patients)*100:.1f}%)")
    print(f"   • Discontinued patients: {discontinued_patients:,} ({overall_discontinuation_rate:.1f}%)")
    print(f"   • Prescription volume range: {df['prescribed_ocrevus_pats'].min():.0f} - {df['prescribed_ocrevus_pats'].max():.0f}")

    # Key findings
    print(f"\n🔍 KEY FINDINGS:")

    # 1. Descriptive statistics insights
    print(f"\n1️⃣ PRESCRIPTION VOLUME CHARACTERISTICS:")
    print(f"   • Average physician prescription volume: {desc_stats['overall_stats']['mean']:.1f} patients")
    print(f"   • Median physician prescription volume: {desc_stats['overall_stats']['50%']:.1f} patients")
    print(f"   • Distribution shape: {desc_stats['skewness']:.2f} (skewness)")
    print(f"   • Outliers detected: {desc_stats['outliers_count']:,} patients ({desc_stats['outliers_percentage']:.1f}%)")

    # 2. Group comparison insights
    print(f"\n2️⃣ GROUP COMPARISON INSIGHTS:")
    active_mean = status_analysis['active_stats']['mean']
    discontinued_mean = status_analysis['discontinued_stats']['mean']
    mean_diff = status_analysis['mean_difference']

    print(f"   • Active patients - Average physician volume: {active_mean:.1f} patients")
    print(f"   • Discontinued patients - Average physician volume: {discontinued_mean:.1f} patients")
    print(f"   • Difference (Active - Discontinued): {mean_diff:.1f} patients")

    if mean_diff > 0:
        comparison_desc = "HIGHER prescription volumes for active patients"
    elif mean_diff < 0:
        comparison_desc = "LOWER prescription volumes for active patients"
    else:
        comparison_desc = "NO difference in prescription volumes"

    print(f"   • Interpretation: {comparison_desc}")

    # 3. Statistical significance
    print(f"\n3️⃣ STATISTICAL SIGNIFICANCE:")
    if test_results['mannwhitney_pvalue'] is not None:
        print(f"   • Mann-Whitney U test p-value: {test_results['mannwhitney_pvalue']:.6f}")
        significance = "SIGNIFICANT" if test_results['mannwhitney_pvalue'] < 0.05 else "NOT SIGNIFICANT"
        print(f"   • Statistical significance: {significance}")

    if test_results['pearson_r'] is not None:
        print(f"   • Pearson correlation: r = {test_results['pearson_r']:.4f}")
        print(f"   • Correlation p-value: {test_results['pearson_p']:.6f}")

    # 4. Segmentation insights
    print(f"\n4️⃣ SEGMENTATION ANALYSIS:")
    if 'discontinuation_by_category' in segmentation_results:
        rates_by_category = segmentation_results['discontinuation_by_category']['discontinuation_rate']
        print(f"   • Discontinuation rates by prescription volume:")
        for category, rate in rates_by_category.items():
            print(f"     - {category}: {rate:.1f}%")

        trend_direction = segmentation_results['trend_direction']
        if trend_direction < -1:
            trend_desc = "DECREASING trend (supports hypothesis)"
        elif trend_direction > 1:
            trend_desc = "INCREASING trend (contradicts hypothesis)"
        else:
            trend_desc = "NO clear trend"

        print(f"   • Trend across categories: {trend_desc}")

    # Hypothesis validation
    print(f"\n🎯 HYPOTHESIS VALIDATION:")
    print(f"   Hypothesis: Higher prescription counts by physicians are associated with")
    print(f"   lower patient discontinuation rates")

    # Determine overall conclusion
    evidence_count = 0
    evidence_details = []

    # Evidence 1: Mean difference
    if mean_diff > 0:
        evidence_count += 1
        evidence_details.append("✅ Active patients have physicians with higher prescription volumes")
    else:
        evidence_details.append("❌ Active patients do not have physicians with higher prescription volumes")

    # Evidence 2: Statistical significance
    if test_results['mannwhitney_pvalue'] is not None and test_results['mannwhitney_pvalue'] < 0.05:
        evidence_count += 1
        evidence_details.append("✅ Statistically significant difference found")
    else:
        evidence_details.append("❌ No statistically significant difference found")

    # Evidence 3: Correlation direction
    if test_results['pearson_r'] is not None and test_results['pearson_r'] < 0:
        evidence_count += 1
        evidence_details.append("✅ Negative correlation supports hypothesis")
    elif test_results['pearson_r'] is not None and test_results['pearson_r'] > 0:
        evidence_details.append("❌ Positive correlation contradicts hypothesis")
    else:
        evidence_details.append("⚠️ Correlation inconclusive")

    # Evidence 4: Trend analysis
    if segmentation_results['trend_direction'] < -1:
        evidence_count += 1
        evidence_details.append("✅ Decreasing discontinuation trend with higher prescription volumes")
    elif segmentation_results['trend_direction'] > 1:
        evidence_details.append("❌ Increasing discontinuation trend with higher prescription volumes")
    else:
        evidence_details.append("⚠️ No clear trend in discontinuation rates")

    print(f"\n   EVIDENCE SUMMARY:")
    for detail in evidence_details:
        print(f"   {detail}")

    print(f"\n   OVERALL CONCLUSION:")
    if evidence_count >= 3:
        conclusion = "STRONGLY SUPPORTED"
        recommendation = "Strong evidence suggests physician experience (measured by prescription volume) is associated with better patient retention."
    elif evidence_count >= 2:
        conclusion = "MODERATELY SUPPORTED"
        recommendation = "Moderate evidence suggests a relationship between physician prescription volume and patient retention."
    elif evidence_count >= 1:
        conclusion = "WEAKLY SUPPORTED"
        recommendation = "Limited evidence for the relationship between physician prescription volume and patient retention."
    else:
        conclusion = "NOT SUPPORTED"
        recommendation = "No evidence found for the relationship between physician prescription volume and patient retention."

    print(f"   🏆 Hypothesis is: {conclusion}")
    print(f"   💡 Recommendation: {recommendation}")

    # Business implications
    print(f"\n💼 BUSINESS IMPLICATIONS & RECOMMENDATIONS:")

    if evidence_count >= 2:
        print(f"   1. 🎯 PHYSICIAN TRAINING: Focus on training less experienced physicians")
        print(f"   2. 📊 MONITORING: Track physician prescription volumes as a quality metric")
        print(f"   3. 🤝 MENTORSHIP: Pair new physicians with experienced ones")
        print(f"   4. 📈 RESOURCE ALLOCATION: Prioritize support for low-volume physicians")
    else:
        print(f"   1. 🔍 FURTHER INVESTIGATION: Explore other factors affecting discontinuation")
        print(f"   2. 📊 DATA COLLECTION: Gather more detailed physician experience metrics")
        print(f"   3. 🎯 ALTERNATIVE STRATEGIES: Focus on patient-specific factors")

    print(f"\n   📋 NEXT STEPS:")
    print(f"   • Conduct longitudinal analysis to establish causality")
    print(f"   • Investigate physician training and certification levels")
    print(f"   • Analyze patient satisfaction scores by physician experience")
    print(f"   • Develop physician experience-based intervention strategies")

    # Save summary to file
    summary_filename = "prescribed_ocrevus_pats_analysis_summary.txt"
    with open(summary_filename, 'w', encoding='utf-8') as f:
        f.write("OCREVUS THERAPY: PHYSICIAN PRESCRIPTION VOLUME vs PATIENT DISCONTINUATION\n")
        f.write("=" * 80 + "\n\n")
        f.write(f"Analysis Date: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Total Patients: {total_patients:,}\n")
        f.write(f"Overall Discontinuation Rate: {overall_discontinuation_rate:.1f}%\n\n")
        f.write(f"Hypothesis: {conclusion}\n")
        f.write(f"Evidence Score: {evidence_count}/4\n\n")
        f.write("Key Findings:\n")
        for detail in evidence_details:
            f.write(f"- {detail}\n")
        f.write(f"\nRecommendation: {recommendation}\n")

    print(f"\n📄 Summary report saved as '{summary_filename}'")

if __name__ == "__main__":
    # Load and preprocess data
    df = load_and_preprocess_data()

    if df is not None:
        # Generate descriptive statistics
        desc_stats = generate_descriptive_statistics(df)

        # Analyze by patient status
        status_analysis = analyze_by_patient_status(df)

        # Perform statistical tests
        test_results = perform_statistical_tests(df)

        # Create visualizations
        create_distribution_visualizations(df)

        # Perform segmentation analysis
        segmentation_results = perform_segmentation_analysis(df)

        # Create relationship visualizations
        create_relationship_visualizations(df)

        # Generate comprehensive summary
        generate_comprehensive_summary(df, desc_stats, status_analysis, test_results, segmentation_results)

        print(f"\n🎉 COMPREHENSIVE ANALYSIS COMPLETED SUCCESSFULLY!")
        print(f"📊 All visualizations saved and summary report generated.")
