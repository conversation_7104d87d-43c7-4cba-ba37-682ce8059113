#!/usr/bin/env python3
"""
Comprehensive Exploratory Data Analysis (EDA) for EDSS Test Value Feature
in Ocrevus Therapy Dataset

This script performs detailed analysis of the relationship between EDSS test values
and patient therapy discontinuation status.

Author: Data Science Team
Date: 2024
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from scipy.stats import chi2_contingency, mannwhitneyu, ttest_ind
import warnings
warnings.filterwarnings('ignore')

# Set style for better visualizations
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

def load_and_preprocess_data(file_path='ocrevus_switch_alerts.csv'):
    """
    Load the dataset and perform initial preprocessing for EDSS test value analysis.

    Parameters:
    -----------
    file_path : str
        Path to the CSV file containing the Ocrevus data

    Returns:
    --------
    pd.DataFrame
        Preprocessed dataframe with clean edss_test_value and patient_status columns
    """
    print("=" * 80)
    print("OCREVUS THERAPY: EDSS TEST VALUE vs DISCONTINUATION ANALYSIS")
    print("=" * 80)

    # Load the dataset
    try:
        df = pd.read_csv(file_path)
        print(f"✓ Dataset loaded successfully: {df.shape[0]:,} rows × {df.shape[1]} columns")
    except FileNotFoundError:
        print(f"❌ Error: File '{file_path}' not found.")
        return None

    # Check for required columns
    required_cols = ['edss_test_value', 'patientsubstatus']
    missing_cols = [col for col in required_cols if col not in df.columns]
    if missing_cols:
        print(f"❌ Error: Missing required columns: {missing_cols}")
        return None

    print(f"✓ Required columns found: {required_cols}")

    # Data preprocessing
    print("\n" + "-" * 60)
    print("DATA PREPROCESSING")
    print("-" * 60)

    # 1. Examine original EDSS test value data
    print(f"Original edss_test_value data type: {df['edss_test_value'].dtype}")
    print(f"Total records: {len(df):,}")

    # Show sample of original values
    print(f"\nSample of original EDSS test values:")
    sample_values = df['edss_test_value'].value_counts().head(10)
    for value, count in sample_values.items():
        print(f"   • '{value}': {count:,} patients")

    # 2. Convert EDSS test values to numeric using pandas to_numeric with errors='coerce'
    print(f"\n🔄 Converting EDSS test values to numeric...")
    df['edss_test_value_numeric'] = pd.to_numeric(df['edss_test_value'], errors='coerce')

    # Check conversion results
    total_rows = len(df)
    missing_after_conversion = df['edss_test_value_numeric'].isna().sum()
    valid_numeric = total_rows - missing_after_conversion

    print(f"✓ Conversion completed:")
    print(f"   • Valid numeric values: {valid_numeric:,} ({valid_numeric/total_rows*100:.1f}%)")
    print(f"   • Invalid/missing values: {missing_after_conversion:,} ({missing_after_conversion/total_rows*100:.1f}%)")

    # 3. Remove rows with NaN values in edss_test_value_numeric as per requirements
    if missing_after_conversion > 0:
        print(f"\n🧹 Removing {missing_after_conversion:,} rows with invalid EDSS values...")
        df_clean = df.dropna(subset=['edss_test_value_numeric']).copy()
        print(f"✓ Clean dataset shape: {df_clean.shape}")
    else:
        df_clean = df.copy()
        print(f"✓ No missing values to remove")

    # 4. Create binary patient_status from patientsubstatus
    print(f"\n🏷️  Creating binary patient status...")
    print(f"Original patientsubstatus categories: {df_clean['patientsubstatus'].nunique()}")

    # Create simplified status: Active vs Discontinued
    df_clean['patient_status'] = df_clean['patientsubstatus'].apply(
        lambda x: 'Discontinued' if 'Discontinued' in str(x) else 'Active'
    )

    print(f"✓ Binary patient status created:")
    status_counts = df_clean['patient_status'].value_counts()
    for status, count in status_counts.items():
        percentage = (count / len(df_clean)) * 100
        print(f"   • {status}: {count:,} patients ({percentage:.1f}%)")

    # 5. Final data validation
    print(f"\n📊 Final dataset summary:")
    print(f"   • Total patients: {len(df_clean):,}")
    print(f"   • EDSS value range: {df_clean['edss_test_value_numeric'].min():.1f} - {df_clean['edss_test_value_numeric'].max():.1f}")
    print(f"   • Active patients: {len(df_clean[df_clean['patient_status'] == 'Active']):,}")
    print(f"   • Discontinued patients: {len(df_clean[df_clean['patient_status'] == 'Discontinued']):,}")

    return df_clean

def display_basic_statistics(df):
    """
    Display basic statistics of the cleaned EDSS test value feature.

    Parameters:
    -----------
    df : pd.DataFrame
        The preprocessed dataframe
    """
    print("\n" + "-" * 60)
    print("BASIC STATISTICS: EDSS TEST VALUES")
    print("-" * 60)

    # Overall descriptive statistics
    stats_overall = df['edss_test_value_numeric'].describe()

    print("📈 DESCRIPTIVE STATISTICS (Overall):")
    print(f"   • Count:          {stats_overall['count']:>8.0f}")
    print(f"   • Mean:           {stats_overall['mean']:>8.2f}")
    print(f"   • Median:         {stats_overall['50%']:>8.2f}")
    print(f"   • Standard Dev:   {stats_overall['std']:>8.2f}")
    print(f"   • Minimum:        {stats_overall['min']:>8.1f}")
    print(f"   • Maximum:        {stats_overall['max']:>8.1f}")
    print(f"   • 25th Percentile:{stats_overall['25%']:>8.2f}")
    print(f"   • 75th Percentile:{stats_overall['75%']:>8.2f}")

    # Additional statistics
    mode_val = df['edss_test_value_numeric'].mode()
    mode_str = f"{mode_val.iloc[0]:.1f}" if not mode_val.empty else "N/A"
    skewness = df['edss_test_value_numeric'].skew()
    kurtosis = df['edss_test_value_numeric'].kurtosis()

    print(f"\n📊 DISTRIBUTION CHARACTERISTICS:")
    print(f"   • Mode:           {mode_str:>8}")
    print(f"   • Skewness:       {skewness:>8.3f} ({'Right-skewed' if skewness > 0.5 else 'Left-skewed' if skewness < -0.5 else 'Approximately symmetric'})")
    print(f"   • Kurtosis:       {kurtosis:>8.3f} ({'Heavy-tailed' if kurtosis > 0 else 'Light-tailed'})")

    # EDSS score interpretation
    print(f"\n🏥 CLINICAL INTERPRETATION:")
    print(f"   • EDSS scores range from 0 (normal) to 10 (death due to MS)")
    print(f"   • Lower scores (0-3.5) indicate minimal to moderate disability")
    print(f"   • Higher scores (4.0+) indicate significant disability requiring assistance")

    return stats_overall

def analyze_by_patient_status(df):
    """
    Analyze EDSS test values by patient status (Active vs Discontinued).

    Parameters:
    -----------
    df : pd.DataFrame
        The preprocessed dataframe

    Returns:
    --------
    dict
        Statistical test results
    """
    print("\n" + "-" * 60)
    print("ANALYSIS BY PATIENT STATUS")
    print("-" * 60)

    # Group by patient status
    active_edss = df[df['patient_status'] == 'Active']['edss_test_value_numeric']
    discontinued_edss = df[df['patient_status'] == 'Discontinued']['edss_test_value_numeric']

    print("📊 DESCRIPTIVE STATISTICS BY GROUP:")
    print("\n🟢 ACTIVE PATIENTS:")
    active_stats = active_edss.describe()
    print(f"   • Count:    {active_stats['count']:>8.0f}")
    print(f"   • Mean:     {active_stats['mean']:>8.2f}")
    print(f"   • Median:   {active_stats['50%']:>8.2f}")
    print(f"   • Std Dev:  {active_stats['std']:>8.2f}")
    print(f"   • Min:      {active_stats['min']:>8.1f}")
    print(f"   • Max:      {active_stats['max']:>8.1f}")

    print("\n🔴 DISCONTINUED PATIENTS:")
    disc_stats = discontinued_edss.describe()
    print(f"   • Count:    {disc_stats['count']:>8.0f}")
    print(f"   • Mean:     {disc_stats['mean']:>8.2f}")
    print(f"   • Median:   {disc_stats['50%']:>8.2f}")
    print(f"   • Std Dev:  {disc_stats['std']:>8.2f}")
    print(f"   • Min:      {disc_stats['min']:>8.1f}")
    print(f"   • Max:      {disc_stats['max']:>8.1f}")

    # Statistical tests
    print(f"\n🔬 STATISTICAL TESTS:")

    # Mann-Whitney U test (non-parametric)
    statistic_mw, p_value_mw = mannwhitneyu(active_edss, discontinued_edss, alternative='two-sided')
    print(f"\n   📋 Mann-Whitney U Test:")
    print(f"      • Test statistic: {statistic_mw:>12,.0f}")
    print(f"      • P-value:        {p_value_mw:>12.6f}")
    print(f"      • Significance:   {'Significant' if p_value_mw < 0.05 else 'Not significant'} (α = 0.05)")

    # Independent t-test (parametric)
    statistic_t, p_value_t = ttest_ind(active_edss, discontinued_edss)
    print(f"\n   📋 Independent t-test:")
    print(f"      • Test statistic: {statistic_t:>12.3f}")
    print(f"      • P-value:        {p_value_t:>12.6f}")
    print(f"      • Significance:   {'Significant' if p_value_t < 0.05 else 'Not significant'} (α = 0.05)")

    # Effect size (Cohen's d)
    pooled_std = np.sqrt(((len(active_edss) - 1) * active_stats['std']**2 +
                         (len(discontinued_edss) - 1) * disc_stats['std']**2) /
                        (len(active_edss) + len(discontinued_edss) - 2))
    cohens_d = (active_stats['mean'] - disc_stats['mean']) / pooled_std

    print(f"\n   📋 Effect Size (Cohen's d):")
    print(f"      • Cohen's d:      {cohens_d:>12.3f}")
    effect_interpretation = "Small" if abs(cohens_d) < 0.5 else "Medium" if abs(cohens_d) < 0.8 else "Large"
    print(f"      • Interpretation: {effect_interpretation:>12} effect")

    return {
        'mann_whitney_u': statistic_mw,
        'mann_whitney_p': p_value_mw,
        't_test_stat': statistic_t,
        't_test_p': p_value_t,
        'cohens_d': cohens_d,
        'active_stats': active_stats,
        'discontinued_stats': disc_stats
    }

def create_comprehensive_visualizations(df):
    """
    Create comprehensive visualizations for EDSS test value analysis.

    Parameters:
    -----------
    df : pd.DataFrame
        The preprocessed dataframe
    """
    print("\n" + "-" * 60)
    print("CREATING VISUALIZATIONS")
    print("-" * 60)

    # Set up the figure with subplots
    fig = plt.figure(figsize=(20, 16))

    # 1. Histogram of EDSS values by patient status
    plt.subplot(3, 3, 1)
    for status in ['Active', 'Discontinued']:
        data = df[df['patient_status'] == status]['edss_test_value_numeric']
        plt.hist(data, alpha=0.7, label=status, bins=20, density=True)
    plt.xlabel('EDSS Test Value')
    plt.ylabel('Density')
    plt.title('Distribution of EDSS Values by Patient Status')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # 2. Box plot comparing EDSS values between groups
    plt.subplot(3, 3, 2)
    sns.boxplot(data=df, x='patient_status', y='edss_test_value_numeric')
    plt.title('EDSS Values: Active vs Discontinued Patients')
    plt.ylabel('EDSS Test Value')
    plt.xlabel('Patient Status')

    # 3. Violin plot for distribution comparison
    plt.subplot(3, 3, 3)
    sns.violinplot(data=df, x='patient_status', y='edss_test_value_numeric')
    plt.title('EDSS Value Distribution (Violin Plot)')
    plt.ylabel('EDSS Test Value')
    plt.xlabel('Patient Status')

    # 4. Bar plot of discontinuation rates by EDSS ranges
    plt.subplot(3, 3, 4)
    # Create EDSS ranges
    df['edss_range'] = pd.cut(df['edss_test_value_numeric'],
                             bins=[0, 2, 4, 6, 10],
                             labels=['0-2 (Minimal)', '2-4 (Moderate)', '4-6 (Severe)', '6+ (Very Severe)'],
                             include_lowest=True)

    # Calculate discontinuation rates by range
    disc_rates = df.groupby('edss_range')['patient_status'].apply(
        lambda x: (x == 'Discontinued').sum() / len(x) * 100
    ).reset_index()
    disc_rates.columns = ['edss_range', 'discontinuation_rate']

    bars = plt.bar(range(len(disc_rates)), disc_rates['discontinuation_rate'],
                   color=['green', 'yellow', 'orange', 'red'], alpha=0.7)
    plt.xlabel('EDSS Range')
    plt.ylabel('Discontinuation Rate (%)')
    plt.title('Discontinuation Rate by EDSS Severity')
    plt.xticks(range(len(disc_rates)), disc_rates['edss_range'], rotation=45)

    # Add value labels on bars
    for i, bar in enumerate(bars):
        height = bar.get_height()
        plt.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                f'{height:.1f}%', ha='center', va='bottom')

    # 5. Count plot of patients by EDSS ranges and status
    plt.subplot(3, 3, 5)
    sns.countplot(data=df, x='edss_range', hue='patient_status')
    plt.title('Patient Count by EDSS Range and Status')
    plt.xlabel('EDSS Range')
    plt.ylabel('Number of Patients')
    plt.xticks(rotation=45)
    plt.legend(title='Patient Status')

    # 6. Scatter plot with jitter
    plt.subplot(3, 3, 6)
    # Add jitter for better visualization
    jitter_strength = 0.1
    for i, status in enumerate(['Active', 'Discontinued']):
        data = df[df['patient_status'] == status]['edss_test_value_numeric']
        y_jitter = np.random.normal(i, jitter_strength, len(data))
        plt.scatter(data, y_jitter, alpha=0.6, label=status, s=20)

    plt.xlabel('EDSS Test Value')
    plt.ylabel('Patient Status (with jitter)')
    plt.title('EDSS Values Distribution by Status')
    plt.yticks([0, 1], ['Active', 'Discontinued'])
    plt.legend()
    plt.grid(True, alpha=0.3)

    # 7. Cumulative distribution function
    plt.subplot(3, 3, 7)
    for status in ['Active', 'Discontinued']:
        data = df[df['patient_status'] == status]['edss_test_value_numeric'].sort_values()
        y = np.arange(1, len(data) + 1) / len(data)
        plt.plot(data, y, label=status, linewidth=2)

    plt.xlabel('EDSS Test Value')
    plt.ylabel('Cumulative Probability')
    plt.title('Cumulative Distribution Function')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # 8. Mean EDSS by status with error bars
    plt.subplot(3, 3, 8)
    means = df.groupby('patient_status')['edss_test_value_numeric'].mean()
    stds = df.groupby('patient_status')['edss_test_value_numeric'].std()

    bars = plt.bar(means.index, means.values, yerr=stds.values,
                   capsize=5, alpha=0.7, color=['lightblue', 'lightcoral'])
    plt.ylabel('Mean EDSS Test Value')
    plt.xlabel('Patient Status')
    plt.title('Mean EDSS Values with Standard Deviation')

    # Add value labels
    for i, (bar, mean_val, std_val) in enumerate(zip(bars, means.values, stds.values)):
        plt.text(bar.get_x() + bar.get_width()/2., bar.get_height() + std_val + 0.1,
                f'{mean_val:.2f}±{std_val:.2f}', ha='center', va='bottom')

    # 9. Heatmap of EDSS ranges vs patient status
    plt.subplot(3, 3, 9)
    crosstab = pd.crosstab(df['edss_range'], df['patient_status'], normalize='index') * 100
    sns.heatmap(crosstab, annot=True, fmt='.1f', cmap='RdYlBu_r',
                cbar_kws={'label': 'Percentage (%)'})
    plt.title('Patient Status Distribution by EDSS Range (%)')
    plt.xlabel('Patient Status')
    plt.ylabel('EDSS Range')

    plt.tight_layout()
    plt.savefig('edss_test_value_comprehensive_analysis.png', dpi=300, bbox_inches='tight')
    print("✓ Comprehensive visualization saved as 'edss_test_value_comprehensive_analysis.png'")

    return fig

def calculate_discontinuation_rates_by_edss(df):
    """
    Calculate and display discontinuation rates by EDSS value ranges.

    Parameters:
    -----------
    df : pd.DataFrame
        The preprocessed dataframe
    """
    print("\n" + "-" * 60)
    print("DISCONTINUATION RATES BY EDSS VALUES")
    print("-" * 60)

    # Overall discontinuation rate
    overall_disc_rate = (df['patient_status'] == 'Discontinued').mean() * 100
    print(f"📊 Overall discontinuation rate: {overall_disc_rate:.1f}%")

    # Discontinuation rates by EDSS ranges
    print(f"\n📋 Discontinuation rates by EDSS severity ranges:")

    edss_ranges = [
        (0, 2, "Minimal disability (0-2)"),
        (2, 4, "Moderate disability (2-4)"),
        (4, 6, "Severe disability (4-6)"),
        (6, 10, "Very severe disability (6+)")
    ]

    for min_val, max_val, description in edss_ranges:
        if min_val == 6:  # For the last range, include values >= 6
            subset = df[df['edss_test_value_numeric'] >= min_val]
        else:
            subset = df[(df['edss_test_value_numeric'] >= min_val) &
                       (df['edss_test_value_numeric'] < max_val)]

        if len(subset) > 0:
            disc_rate = (subset['patient_status'] == 'Discontinued').mean() * 100
            total_patients = len(subset)
            disc_patients = (subset['patient_status'] == 'Discontinued').sum()

            print(f"   • {description}:")
            print(f"     - Total patients: {total_patients:,}")
            print(f"     - Discontinued: {disc_patients:,} ({disc_rate:.1f}%)")
            print(f"     - Difference from overall: {disc_rate - overall_disc_rate:+.1f} percentage points")
        else:
            print(f"   • {description}: No patients in this range")

    # Statistical significance test for high vs low EDSS
    print(f"\n🔬 Statistical test: High vs Low EDSS discontinuation rates")

    # Define high and low EDSS groups
    low_edss = df[df['edss_test_value_numeric'] < 4]
    high_edss = df[df['edss_test_value_numeric'] >= 4]

    if len(low_edss) > 0 and len(high_edss) > 0:
        # Create contingency table
        low_disc = (low_edss['patient_status'] == 'Discontinued').sum()
        low_active = (low_edss['patient_status'] == 'Active').sum()
        high_disc = (high_edss['patient_status'] == 'Discontinued').sum()
        high_active = (high_edss['patient_status'] == 'Active').sum()

        contingency_table = np.array([[low_disc, low_active],
                                     [high_disc, high_active]])

        chi2, p_value, dof, expected = chi2_contingency(contingency_table)

        print(f"   • Low EDSS (<4): {low_disc}/{len(low_edss)} discontinued ({(low_disc/len(low_edss)*100):.1f}%)")
        print(f"   • High EDSS (≥4): {high_disc}/{len(high_edss)} discontinued ({(high_disc/len(high_edss)*100):.1f}%)")
        print(f"   • Chi-square statistic: {chi2:.3f}")
        print(f"   • P-value: {p_value:.6f}")
        print(f"   • Significance: {'Significant' if p_value < 0.05 else 'Not significant'} (α = 0.05)")

def generate_insights_and_recommendations(df, test_results):
    """
    Generate clinical insights and recommendations based on the analysis.

    Parameters:
    -----------
    df : pd.DataFrame
        The preprocessed dataframe
    test_results : dict
        Statistical test results
    """
    print("\n" + "=" * 80)
    print("CLINICAL INSIGHTS AND RECOMMENDATIONS")
    print("=" * 80)

    # Key findings
    active_mean = test_results['active_stats']['mean']
    disc_mean = test_results['discontinued_stats']['mean']
    p_value = test_results['mann_whitney_p']
    cohens_d = test_results['cohens_d']

    print("🔍 KEY FINDINGS:")
    print(f"   • Active patients have mean EDSS: {active_mean:.2f}")
    print(f"   • Discontinued patients have mean EDSS: {disc_mean:.2f}")
    print(f"   • Difference: {abs(disc_mean - active_mean):.2f} points")
    print(f"   • Statistical significance: {'Yes' if p_value < 0.05 else 'No'} (p = {p_value:.6f})")
    print(f"   • Effect size: {abs(cohens_d):.3f} ({'Small' if abs(cohens_d) < 0.5 else 'Medium' if abs(cohens_d) < 0.8 else 'Large'})")

    # Clinical interpretation
    print(f"\n🏥 CLINICAL INTERPRETATION:")

    if p_value < 0.05:
        if disc_mean > active_mean:
            print("   • Patients with higher EDSS scores are more likely to discontinue therapy")
            print("   • This suggests that disease severity may influence treatment adherence")
            print("   • Higher disability levels may lead to treatment challenges or side effects")
        else:
            print("   • Patients with lower EDSS scores are more likely to discontinue therapy")
            print("   • This may indicate early treatment switching or intolerance in less disabled patients")
    else:
        print("   • No statistically significant difference in EDSS scores between groups")
        print("   • EDSS score alone may not be a strong predictor of discontinuation")
        print("   • Other factors may be more important for predicting therapy outcomes")

    # Recommendations
    print(f"\n💡 RECOMMENDATIONS:")
    print("   1. Monitor patients with extreme EDSS values more closely")
    print("   2. Consider additional support for patients with high disability scores")
    print("   3. Investigate other factors contributing to discontinuation")
    print("   4. Develop targeted interventions based on EDSS severity levels")
    print("   5. Regular EDSS assessments to track disease progression")

    # Data quality notes
    print(f"\n📝 DATA QUALITY NOTES:")
    total_original = len(df) + (12640 - len(df))  # Approximate original size
    print(f"   • Analysis based on {len(df):,} patients with valid EDSS values")
    print(f"   • Excluded patients with missing/invalid EDSS data")
    print(f"   • Results are representative of patients with recorded EDSS assessments")

if __name__ == "__main__":
    # Run the complete analysis
    df = load_and_preprocess_data()

    if df is not None:
        stats_overall = display_basic_statistics(df)
        test_results = analyze_by_patient_status(df)
        fig = create_comprehensive_visualizations(df)
        calculate_discontinuation_rates_by_edss(df)
        generate_insights_and_recommendations(df, test_results)

        print("\n" + "=" * 80)
        print("ANALYSIS COMPLETE")
        print("=" * 80)
        print("✓ Comprehensive EDSS test value EDA completed successfully")
        print("✓ Visualization saved as 'edss_test_value_comprehensive_analysis.png'")
        print("✓ Statistical analysis and clinical insights generated")
        print("✓ Ready for clinical interpretation and decision-making")
