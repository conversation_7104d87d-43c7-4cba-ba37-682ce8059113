#!/usr/bin/env python
# coding: utf-8

# ## Ocrevus Therapy Exploratory Analysis For Patient Provinces

# #### Data Import and Setup
# 
# Import necessary libraries and load the Ocrevus switch alerts dataset.

# In[1]:


import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns

# Set display options for better output
pd.set_option('display.max_columns', None)
pd.set_option('display.width', None)

# Set matplotlib style
plt.style.use('default')
sns.set_palette("husl")

import warnings
warnings.filterwarnings('ignore')

print("Libraries imported successfully")


# #### Data Loading and Preparation
# 
# Load the dataset, prepare the target feature and create the discontinuation status indicator variable.

# In[2]:


# Load the dataset
etl_output = pd.read_parquet('can_ocrevus_etl_output.parquet')

# Check the dataset shape
print(f"Dataset shape: {etl_output.shape}")


# In[3]:


#Fix the target feature
etl_output['patientsubstatus'] = etl_output['patientsubstatus'].apply(lambda x: 'Discontinued' if 'Discontinued' in x else x)

df = etl_output[~etl_output['patientsubstatus'].str.contains('Pending')]

# Check the dataset shape
print(f"Dataset shape: {df.shape}")


# In[4]:


#check the target feature
df['patientsubstatus'].value_counts()


# In[5]:


# Create discontinuation status indicator
df['is_discontinued'] = df['patientsubstatus'].str.contains('Discontinued', na=False)


# In[6]:


df['is_discontinued'].value_counts(dropna=False)


# #### Provincial Discontinuation Rate Analysis
# 
# Calculate and analyze discontinuation rates by province to identify high-risk and low-risk regions.

# In[7]:


# Calculate discontinuation rates by province
province_stats = df.groupby('patientprovince').agg({
        'patientid': 'count',
        'is_discontinued': ['sum', 'mean']
    }).round(3)

# Flatten column names & Convert to percentage
province_stats.columns = ['total_patients', 'discontinued_count', 'discontinuation_rate']
province_stats['discontinuation_rate'] *= 100
    
# Sort by discontinuation rate
province_stats = province_stats.sort_values('discontinuation_rate', ascending=False)


# In[8]:


# Calculate overall discontinuation rate
overall_rate = df['is_discontinued'].mean() * 100
print(f"\nOverall discontinuation rate: {overall_rate:.1f}%")


# In[9]:


#Display Province Stats
print(f"\nProvince Rankings (Highest to Lowest Risk):")
print("-" * 75)
print(f"{'Province':<10} {'Total Patients':<15} {'Discontinued Patients':<22} {'Discontinuation Rate (%)':<15}")
print("-" * 75)
    
for province, row in province_stats.iterrows():
        print(f"{province:<10} {row['total_patients']:<15} {row['discontinued_count']:<22} {row['discontinuation_rate']:<15.1f}")


# In[10]:


# Identify high-risk provinces
high_risk_threshold = overall_rate + 5
high_risk_provinces = province_stats[province_stats['discontinuation_rate'] > high_risk_threshold]
    
print(f"\n HIGH-RISK PROVINCES (>{high_risk_threshold:.1f}%):")
for province, row in high_risk_provinces.iterrows():
    print(f"   • {province}: {row['discontinuation_rate']:.1f}% ({row['discontinued_count']:.0f}/{row['total_patients']:.0f} patients)")


# #### Data Visualization
# 
# Create a bar chart showing discontinuation rates by provinces

# In[11]:


#Visualize discontinuation rates by province
plt.figure(figsize=(12, 6))
        
# Bar chart of discontinuation rates
colors = ['red' if rate > overall_rate else 'steelblue' 
          for rate in province_stats['discontinuation_rate']]
        
bars = plt.bar(province_stats.index, province_stats['discontinuation_rate'], 
               color=colors, alpha=0.7, edgecolor='black')
        
# Add national average line
plt.axhline(y=overall_rate, color='green', linestyle='--', linewidth=2, 
            label=f'National Average ({overall_rate:.1f}%)')
            
# Formatting
plt.title('Ocrevus Discontinuation Rates by Province', fontsize=14, fontweight='bold')
plt.xlabel('Province')
plt.ylabel('Discontinuation Rate (%)')
plt.legend()
            
# Add value labels on bars
for bar, rate in zip(bars, province_stats['discontinuation_rate']):
    plt.text(bar.get_x() + bar.get_width()/2., bar.get_height() + 0.3,
            f'{rate:.1f}%', ha='center', va='bottom', fontsize=9)
            
plt.tight_layout()
#plt.savefig('province_discontinuation_rates.png', dpi=200, bbox_inches='tight')
plt.show()


# #### Demographic Analysis for High-Risk Provinces
# 
# Analyze age and gender patterns in high-risk provinces to understand demographic factors contributing to higher discontinuation rates.

# In[12]:


#Demographic analysis for high risk provinces
provinces_of_interest = high_risk_provinces.index.tolist()

print(f"\nDEMOGRAPHIC ANALYSIS FOR: {', '.join(provinces_of_interest)}")
print("-" * 50)
    
for province in provinces_of_interest:
        province_data = df[df['patientprovince'] == province]
        
        if len(province_data) > 0:
            print(f" {province}:")
            
            # Age analysis
            active_age = province_data[~province_data['is_discontinued']]['age'].mean()
            disc_age = province_data[province_data['is_discontinued']]['age'].mean()
            
            print(f"   Age Analysis:")
            print(f"     • Active patients avg age: {active_age:.1f} years")
            print(f"     • Discontinued patients avg age: {disc_age:.1f} years")
            
            # Gender analysis
            gender_disc = province_data[province_data['is_discontinued']]['gender'].value_counts(normalize=True) * 100
            gender_active = province_data[~province_data['is_discontinued']]['gender'].value_counts(normalize=True) * 100
            
            print(f"   Gender Distribution:")
            print(f"     • Discontinued Patients - Female: {gender_disc['F']:.1f}%, Male: {gender_disc['M']:.1f}%")
            print(f"     • Active Patients - Female: {gender_active['F']:.1f}%, Male: {gender_active['M']:.1f}%")


# ### Key Findings:
# 
# - **Overall discontinuation rate:** 18.3%
# - **High risk provinces:** NS (25.5%), BC (25.4%), NB (24%)
# - **Low risk provinces:** NU (0.0%), PE (8.1%), NL (10.2%)
# - Provinces above national average: 5
# - **Demographic patterns** show discontinued patients are consistently older across most provinces
# - **Gender distribution** varies significantly across provinces
# - **Female patients represent 66-74%** of discontinuations in high-risk provinces
# - **NS shows highest female discontinuation rate:** 73.8%
# - **BC and QC also show high female discontinuation:** ~69%

# ## Exploratory Analysis For Patient Regions

# In[13]:


#Map provinces to regions
region_mapping = {
    'ON': 'ON', 
    'QC': 'QC',
    'BC': 'BC',
    'YT': 'YT',
    'NT': 'NT',
    'NU': 'NU',
    'NB': "Atlantic",
    'NS': "Atlantic",
    'NL': "Atlantic",
    'PE': "Atlantic",
    'SK': 'Prairies',
    'MB': 'Prairies',
    'AB': 'Prairies'
}
df['patientregion'] = df['patientprovince'].map(region_mapping)


# In[14]:


# View the unique values in the new column
print(df['patientregion'].unique())


# In[15]:


# cross-tabulation of provinces and regions
print(pd.crosstab(df['patientprovince'], df['patientregion']))


# #### Regional Discontinuation Rate Analysis
# 
# Calculate and analyze discontinuation rates by region to identify high-risk and low-risk regions.

# In[16]:


# Calculate discontinuation rates by region
region_stats = df.groupby('patientregion').agg({
        'patientid': 'count',
        'is_discontinued': ['sum', 'mean']
    }).round(3)

# Flatten column names & Convert to percentage
region_stats.columns = ['total_patients', 'discontinued_count', 'discontinuation_rate']
region_stats['discontinuation_rate'] *= 100
    
# Sort by discontinuation rate
region_stats = region_stats.sort_values('discontinuation_rate', ascending=False)


# In[17]:


# Display region stats
print(f"\nRegion Rankings (Highest to Lowest Risk):")
print("-" * 75)
print(f"{'Region':<10} {'Total Patients':<15} {'Discontinued Patients':<22} {'Discontinuation Rate (%)':<15}")
print("-" * 75)
    
for region, row in region_stats.iterrows():
        print(f"{region:<10} {row['total_patients']:<15} {row['discontinued_count']:<22} {row['discontinuation_rate']:<15.1f}")


# In[18]:


# Identify high-risk regions
high_risk_threshold = overall_rate + 3
high_risk_regions = region_stats[region_stats['discontinuation_rate'] > high_risk_threshold]
    
print(f"\n HIGH-RISK REGIONS (>{high_risk_threshold:.1f}%):")
for region, row in high_risk_regions.iterrows():
    print(f"   • {region}: {row['discontinuation_rate']:.1f}% ({row['discontinued_count']:.0f}/{row['total_patients']:.0f} patients)")


# #### Data Visualization
# 
# Create a bar chart showing discontinuation rates by regions

# In[19]:


# Visualize discontinuation rates by region
plt.figure(figsize=(12, 6))
        
# Bar chart of discontinuation rates
colors = ['red' if rate > overall_rate else 'steelblue' 
          for rate in region_stats['discontinuation_rate']]
        
bars = plt.bar(region_stats.index, region_stats['discontinuation_rate'], 
               color=colors, alpha=0.7, edgecolor='black')
        
# Add national average line
plt.axhline(y=overall_rate, color='green', linestyle='--', linewidth=2, 
            label=f'National Average ({overall_rate:.1f}%)')
            
# Formatting
plt.title('Ocrevus Discontinuation Rates by Region', fontsize=14, fontweight='bold')
plt.xlabel('Region', fontsize=12)
plt.ylabel('Discontinuation Rate (%)', fontsize=12)
plt.legend()
            
# Add value labels on bars
for bar, rate in zip(bars, region_stats['discontinuation_rate']):
    plt.text(bar.get_x() + bar.get_width()/2., bar.get_height() + 0.3,
            f'{rate:.1f}%', ha='center', va='bottom', fontsize=9)
            
plt.tight_layout()
plt.show()


# #### Demographic Analysis for High-Risk Regions
# 
# Analyze age and gender patterns in high-risk regions to understand demographic factors contributing to higher discontinuation rates.

# In[20]:


# Demographic analysis for high risk regions
regions_of_interest = high_risk_regions.index.tolist()

print(f"\nDEMOGRAPHIC ANALYSIS FOR: {', '.join(regions_of_interest)}")
print("-" * 50)
    
for region in regions_of_interest:
        region_data = df[df['patientregion'] == region]
        
        if len(region_data) > 0:
            print(f" {region}:")
            
            # Age analysis
            active_age = region_data[~region_data['is_discontinued']]['age'].mean()
            disc_age = region_data[region_data['is_discontinued']]['age'].mean()
            
            print(f"   Age Analysis:")
            print(f"     • Active patients avg age: {active_age:.1f} years")
            print(f"     • Discontinued patients avg age: {disc_age:.1f} years")
            
            # Gender analysis
            gender_disc = region_data[region_data['is_discontinued']]['gender'].value_counts(normalize=True) * 100
            gender_active = region_data[~region_data['is_discontinued']]['gender'].value_counts(normalize=True) * 100
            
            print(f"   Gender Distribution:")
            print(f"     • Discontinued Patients - Female: {gender_disc['F']:.1f}%, Male: {gender_disc['M']:.1f}%")
            print(f"     • Active Patients - Female: {gender_active['F']:.1f}%, Male: {gender_active['M']:.1f}%")


# ### Key Findings:
# 
# - **Overall discontinuation rate:** 18.3%
# - **High risk regions:** BC (25.4%), YT (22.2%), QC(22.1%), Atlantic(22.1%)
# - Regions above national average discontinuation rate: 4
# - **Demographic patterns** show discontinued patients are consistently older across most regions
# - **Gender distribution** varies significantly across regions
# - **Female patients represent 68-87%** of discontinuations in high-risk regions
# - **YT shows highest female discontinuation rate:** 87.5%
# - **BC and QC also show high female discontinuation:** ~69%

# ## Exploratory Analysis For Total Infusions

# In[21]:


# Check for missing values in total_infusions
missing_infusions = df['total_infusions'].isna().sum()
if missing_infusions > 0:
    print(f"{missing_infusions} missing values in total_infusions")
    # Remove rows with missing total_infusions
    df = df.dropna(subset=['total_infusions'])
    print(f"Removed rows with missing total_infusions. New shape: {df.shape}")
else:
    print("No missing values in total_infusions")


# In[22]:


#Simplify patientsubstatus to Active/Discontinued
df['patient_status'] = df['patientsubstatus'].apply(lambda x: 'Discontinued' if 'Discontinued' in str(x) else 'Active')


# In[23]:


status_counts = df['patient_status'].value_counts()
for status, count in status_counts.items():
    percentage = (count / len(df)) * 100
    print(f"{status}: {count:,} patients ({percentage:.1f}%)")


# In[24]:


#Range of Infusions
print(f"Total infusions range: {df['total_infusions'].min()} - {df['total_infusions'].max()}")


# In[25]:


# overall distribution of total_infusions.
df['total_infusions'].describe()


# In[26]:


# Calculate descriptive statistics by patient status
stats = df.groupby('patient_status')['total_infusions'].describe().round(2)
print("Descriptive Statistics for Total Infusions by Patient Status: \n")
print(stats)


# In[27]:


# Bar plot showing mean infusions by status
plt.figure(figsize=(10, 6))

ax = sns.barplot(x='patient_status', y='total_infusions', hue='patient_status', data=df, palette='viridis')

# Add mean values as text annotations
for i, status in enumerate(stats.index):
    mean_val = stats.loc[status, 'mean']
    ax.text(i, mean_val + 0.1, f'Mean: {mean_val:.2f}', ha='center')

# Add labels and title
plt.title('Comparison of Total Infusions Between Active and Discontinued Patients', fontsize=14, fontweight='bold')
plt.xlabel('Patient Status', fontsize=12)
plt.ylabel('Number of Infusions', fontsize=12)
plt.tight_layout()
plt.show()


# In[28]:


# Boxplot for distribution comparison
plt.figure(figsize=(10, 6))

sns.boxplot(x='patient_status', y='total_infusions', hue='patient_status', data=df, palette='viridis')
plt.title('Distribution of Total Infusions by Patient Status', fontsize=14, fontweight='bold')
plt.xlabel('Patient Status', fontsize=12)
plt.ylabel('Number of Infusions', fontsize=12)
plt.tight_layout()
plt.show()


# In[29]:


# Categorize the Infusions into ranges and perform analysis
print(f"\n INFUSION RANGES:")

ranges = [
    (1, 3, "Early therapy (1-3 infusions)"),
    (4, 6, "Short-term therapy (4-6 infusions)"),
    (7, 12, "Medium-term therapy (7-12 infusions)"),
    (13, float('inf'), "Long-term therapy (13+ infusions)")
]   

for min_inf, max_inf, label in ranges:
    count = len(df[(df['total_infusions'] >= min_inf) & (df['total_infusions'] <= max_inf)])
    percentage = (count / len(df)) * 100
    print(f"   • {label}: {count} patients ({percentage:.1f}%)")


# In[30]:


# Calculate discontinuation ranges by therapy duration
for min_inf, max_inf, label in ranges:
        range_data = df[(df['total_infusions'] >= min_inf) & (df['total_infusions'] <= max_inf)]
        total_patients = len(range_data)
        discontinued_patients = len(range_data[range_data['patient_status'] == 'Discontinued'])
        discontinuation_rate = (discontinued_patients / total_patients) * 100
        print(f"   • {label}: {discontinuation_rate:.1f}% ({discontinued_patients}/{total_patients} patients)")


# #### Key Findings
# 
# - Average infusions of **Active patients** : 8
# - Average infusions of **Discontinued patients** : 5.6
# - Difference in average infusions : -2.4

# #### Primary Insight:
# - Discontinued patients show lower infusion counts compared to active patients.
# - Patients with fewer infusions are at higher risk of discontinuation.

# ## Exploratory Analysis For EDSS Count

# In[31]:


df_edss_mri = df.dropna(subset=['edss_count'])
print(f"Removed rows with missing edss_count. New shape: {df_edss_mri.shape}")


# In[32]:


# Data Validation
print(f" Missing EDSS: {df_edss_mri['edss_count'].isna().sum()}")
print(f" EDSS count range: {df_edss_mri['edss_count'].min()} - {df_edss_mri['edss_count'].max()}")


# In[33]:


# Analyze the overall distribution of edss_count
df_edss_mri['edss_count'].describe()


# In[34]:


# EDSS count frequency distribution
value_counts = df_edss_mri['edss_count'].value_counts().sort_index()
total_patients = len(df_edss_mri)

for count, freq in value_counts.items():
    percentage = (freq / total_patients) * 100
    print(f"   • {count} assessments: {freq:,} patients ({percentage:.1f}%)")


# In[35]:


# Calculate descriptive statistics by patient status
stats_by_status = df_edss_mri.groupby('patient_status')['edss_count'].describe().round(2)
print("Descriptive Statistics for EDSS Count by Patient Status: \n")
print(stats_by_status)


# In[36]:


# Calculate discontinuation rates by EDSS count
discontinuation_rates = []
edss_counts = []
for edss_count in sorted(df_edss_mri['edss_count'].unique()):
    subset = df_edss_mri[df_edss_mri['edss_count'] == edss_count]
    total_patients = len(subset)
    discontinued_patients = len(subset[subset['patient_status'] == 'Discontinued'])
    rate = (discontinued_patients / total_patients) * 100
    discontinuation_rates.append(rate)
    edss_counts.append(edss_count)
    print(f"   • {edss_count} assessments: {discontinued_patients}/{total_patients} patients ({rate:.1f}%)")


# In[37]:


fig = plt.figure(figsize=(20, 16))

# Plot discontinuation rate by EDSS count
plt.subplot(2, 2, 1)
plt.bar(edss_counts, discontinuation_rates, color='orange', alpha=0.7)
plt.title('Discontinuation Rate by EDSS Count', fontsize=14, fontweight='bold')
plt.xlabel('Number of EDSS Assessments',  fontsize=12)
plt.ylabel('Discontinuation Rate (%)',  fontsize=12)

# Overall EDSS Count Distribution (Histogram)
plt.subplot(2, 2, 2)
plt.hist(df_edss_mri['edss_count'], bins=range(int(df_edss_mri['edss_count'].min()), 
         int(df_edss_mri['edss_count'].max()) + 2), color='skyblue', edgecolor='black')
plt.title('Overall EDSS Count Distribution', fontsize=14, fontweight='bold')
plt.xlabel('Number of EDSS Assessments',  fontsize=12)
plt.ylabel('Number of Patients',  fontsize=12)

# Histogram Comparison: Active vs Discontinued
plt.subplot(2, 2, 3)
sns.countplot(data=df_edss_mri, x='edss_count', hue='patient_status',
            palette={'Active': 'lightblue', 'Discontinued': 'lightcoral'})
plt.title('EDSS Count Distribution Comparison', fontsize=14, fontweight='bold')
plt.xlabel('Number of EDSS Assessments',  fontsize=12)
plt.ylabel('Number of Patients',  fontsize=12)
plt.legend()

# Mean EDSS Count by Status
plt.subplot(2, 2, 4)
ax = sns.barplot(data=df_edss_mri, x='patient_status', y='edss_count', hue='patient_status',
                palette={'Active': 'lightblue', 'Discontinued': 'lightcoral'})
plt.title('Mean EDSS Count by Patient Status', fontsize=14, fontweight='bold')
plt.xlabel('Patient Status', fontsize=12)
plt.ylabel('Average EDSS Count', fontsize=12)

# Add value labels
for i in ax.containers:
    ax.bar_label(i, fmt='%.2f', padding=3)

plt.tight_layout()
plt.show()


# ### Key Findings
# 
# - Overall discontinuation rate: 18.4%
# -  Mean EDSS count for **Active patients**: 1.10
# -  Mean EDSS count for **Discontinued patients**: 1.12
# -  **89.3%** of the patients had just 1 assesment.

# ### Primary Insight
# 
# - Patients with **zero EDSS assessments** show the highest discontinuation risk (23.8%), 5.4 percentage points higher than the overall average.
# - Suggesting that lack of monitoring may be associated with therapy discontinuation.

# ## Exploratory Analysis For MRI Count

# In[38]:


# Data Validation
print(f" Missing MRI: {df_edss_mri['mri_count'].isna().sum()}")
print(f" EDSS count range: {df_edss_mri['mri_count'].min()} - {df_edss_mri['mri_count'].max()}")


# In[39]:


# Analyze the overall distribution of mri_count
df_edss_mri['mri_count'].describe()


# In[40]:


# MRI count frequency distribution
value_counts = df_edss_mri['mri_count'].value_counts().sort_index()
total_patients = len(df_edss_mri)

for count, freq in value_counts.items():
    percentage = (freq / total_patients) * 100
    print(f"   • {count} scans: {freq:,} patients ({percentage:.1f}%)")


# In[41]:


# Calculate descriptive statistics by patient status
stats_by_status = df_edss_mri.groupby('patient_status')['mri_count'].describe().round(2)
print("Descriptive Statistics for MRI Count by Patient Status: \n")
print(stats_by_status)


# In[42]:


# Calculate discontinuation rates by EDSS count
discontinuation_rate = []
mri_counts = []
for mri_count in sorted(df_edss_mri['mri_count'].unique()):
    subset = df_edss_mri[df_edss_mri['mri_count'] == mri_count]
    total_patients = len(subset)
    if len(subset) >= 5:  # Only include groups with at least 5 patients
        discontinued_patients = len(subset[subset['patient_status'] == 'Discontinued'])
        rate = (discontinued_patients / total_patients) * 100
        discontinuation_rate.append(rate)
        mri_counts.append(mri_count)
        print(f"   • {mri_count} scans: {discontinued_patients}/{total_patients} patients ({rate:.1f}%)")


# In[43]:


fig = plt.figure(figsize=(20, 16))

# Plot discontinuation rate by MRI count
plt.subplot(2, 2, 1)
plt.bar(mri_counts, discontinuation_rate, color='purple', alpha=0.7)
plt.title('Discontinuation Rate by MRI Count', fontsize=14, fontweight='bold')
plt.xlabel('Number of MRI scans',  fontsize=12)
plt.ylabel('Discontinuation Rate (%)',  fontsize=12)

# Overall MRI Count Distribution (Histogram)
plt.subplot(2, 2, 2)
plt.hist(df_edss_mri['mri_count'], bins=range(int(df_edss_mri['mri_count'].min()), 
         int(df_edss_mri['mri_count'].max()) + 2), color='lightgreen', edgecolor='black')
plt.title('Overall MRI Count Distribution', fontsize=14, fontweight='bold')
plt.xlabel('Number of MRI Scans',  fontsize=12)
plt.ylabel('Number of Patients',  fontsize=12)

# Histogram Comparison: Active vs Discontinued
plt.subplot(2, 2, 3)
sns.countplot(data=df_edss_mri, x='mri_count', hue='patient_status',
            palette={'Active': 'lightgreen', 'Discontinued': 'lightcoral'})
plt.title('MRI Count Distribution Comparison', fontsize=14, fontweight='bold')
plt.xlabel('Number of MRI Scans',  fontsize=12)
plt.ylabel('Number of Patients',  fontsize=12)
plt.legend()

# Mean EDSS Count by Status
plt.subplot(2, 2, 4)
ax = sns.barplot(data=df_edss_mri, x='patient_status', y='mri_count', hue='patient_status',
                palette={'Active': 'lightgreen', 'Discontinued': 'lightcoral'})
plt.title('Mean MRI Count by Patient Status', fontsize=14, fontweight='bold')
plt.xlabel('Patient Status', fontsize=12)
plt.ylabel('Average MRI Count', fontsize=12)

# Add value labels
for i in ax.containers:
    ax.bar_label(i, fmt='%.2f', padding=3)

plt.tight_layout()
plt.show()


# ### Key Findings
# 
# - Overall discontinuation rate: 18.4%
# -  Mean MRI count for **Active patients**: 0.54
# -  Mean MRI count for **Discontinued patients**: 0.61
# -  Difference in means: 0.07

# ### Primary Insights
# 
# - Discontinued patients have slightly more MRI scans (0.61 vs 0.54), suggesting increased imaging needs before discontinuation.
# - Patients with exactly **2 MRI scans** have a 28.6% discontinuation rate, 10.2 percentage points higher than average. This represent a critical monitoring threshold where disease activity prompts additional imaging before therapy changes.
# - Patients requiring 2+ MRI scans may need enhanced clinical attention.
# - **55.6%** of patients have zero MRI scans, indicating potential gaps in imaging surveillance.
# - Higher MRI frequency likely reflects underlying disease progression requiring enhanced monitoring before discontinuation.

# ## Exploratory Analysis For EDSS Scores

# In[44]:


# Examine original EDSS test value data
print(f"Original edss_test_value data type: {df['edss_test_value'].dtype}")
print(f"Total records: {len(df):,}")


# In[45]:


# Sample of original EDSS test values
print(f"\nSample of original EDSS test values:")
sample_values = df['edss_test_value'].value_counts().head(10)
for value, count in sample_values.items():
    print(f"   • '{value}': {count:,} patients")


# In[46]:


# Convert EDSS test values to numeric
print(f"\n Converting EDSS test values to numeric...")
df['edss_test_value_numeric'] = pd.to_numeric(df['edss_test_value'], errors='coerce')


# In[47]:


# Check conversion results
total_rows = len(df)
missing_after_conversion = df['edss_test_value_numeric'].isna().sum()
valid_numeric = total_rows - missing_after_conversion

print(f"Conversion completed:")
print(f"   • Valid numeric values: {valid_numeric:,} ({valid_numeric/total_rows*100:.1f}%)")
print(f"   • Invalid/missing values: {missing_after_conversion:,} ({missing_after_conversion/total_rows*100:.1f}%)")


# In[48]:


# Remove rows with NaN values in edss_test_value_numeric
print(f"\n Removing {missing_after_conversion:,} rows with invalid EDSS values...")
df_edss = df.dropna(subset=['edss_test_value_numeric']).copy()
print(f" Clean dataset shape: {df_edss.shape}")


# In[49]:


# Check the distribution of patient status
status_counts = df_edss['patient_status'].value_counts()
for status, count in status_counts.items():
    percentage = (count / len(df_edss)) * 100
    print(f"   • {status}: {count:,} patients ({percentage:.1f}%)")


# In[50]:


# Check the range of EDSS scores
print(f"   • EDSS value range: {df_edss['edss_test_value_numeric'].min()} - {df_edss['edss_test_value_numeric'].max()}")


# In[51]:


# Display basic statistics of the cleaned EDSS test value feature
df['edss_test_value_numeric'].describe().round(2)


# In[52]:


print(f"\n CLINICAL INTERPRETATION:")
print(f"   • EDSS scores range from 0 (normal) to 10 (death due to MS)")
print(f"   • 50% of the patients have EDSS Score have less than 2.5")
print(f"   • 75% of the patients have EDSS Score have less than 4")
print(f"   • Lower scores (0-3.5) indicate minimal to moderate disability")
print(f"   • Higher scores (4.0+) indicate significant disability requiring assistance")


# In[53]:


# Calculate descriptive statistics by patient status
stats_by_status = df_edss.groupby('patient_status')['edss_test_value_numeric'].describe().round(2)
print("Descriptive Statistics for EDSS Test Value by Patient Status: \n")
print(stats_by_status)


# In[54]:


# Overall discontinuation rate
overall_disc_rate = (df_edss['patient_status'] == 'Discontinued').mean() * 100
print(f" Overall discontinuation rate: {overall_disc_rate:.1f}%")


# In[55]:


# Categorize the EDSS scores into severity ranges and perform analysis
print(f"\nEDSS severity ranges:")

edss_ranges = [
    (0, 2, "Minimal disability (0-2)"),
    (2, 4, "Moderate disability (2-4)"),
    (4, 6, "Severe disability (4-6)"),
    (6, 10, "Very severe disability (6+)")
]

for min_val, max_val, label in edss_ranges:
    count = len(df_edss[(df_edss['edss_test_value_numeric'] >= min_val) & (df_edss['edss_test_value_numeric'] < max_val)])
    percentage = (count / len(df_edss)) * 100
    print(f"   • {label}: {count} patients ({percentage:.1f}%)")


# In[56]:


# Discontinuation rates by EDSS ranges
print(f"\n Discontinuation rates by EDSS severity ranges:")
for min_val, max_val, label in edss_ranges:
        range_data = df_edss[(df_edss['edss_test_value_numeric'] >= min_val) & (df_edss['edss_test_value_numeric'] < max_val)]
        total_patients = len(range_data)
        discontinued_patients = len(range_data[range_data['patient_status'] == 'Discontinued'])
        discontinuation_rate = (discontinued_patients / total_patients) * 100
        print(f"   • {label}: {discontinuation_rate:.1f}% ({discontinued_patients}/{total_patients} patients)")


# In[57]:


# Create EDSS ranges
df_edss['edss_range'] = pd.cut(df_edss['edss_test_value_numeric'], bins=[0, 2, 4, 6, 10], 
                          labels=['0-2 (Minimal)', '2-4 (Moderate)', '4-6 (Severe)', '6+ (Very Severe)'], include_lowest=True)


# In[58]:


fig = plt.figure(figsize=(20, 16))

# # Histogram of EDSS values by patient status
plt.subplot(2, 2, 1)
plt.hist(df_edss['edss_test_value_numeric'], bins=range(int(df_edss['edss_test_value_numeric'].min()), 
         int(df_edss['edss_test_value_numeric'].max()) + 2), color='lightgreen', edgecolor='black')
plt.title('Distribution of EDSS Values', fontsize=14, fontweight='bold')
plt.xlabel('EDSS Test Value',  fontsize=12)
plt.ylabel('Number of Patients',  fontsize=12)

# Box plot comparing EDSS values between groups
plt.subplot(2, 2, 2)
sns.boxplot(data=df_edss, x='patient_status', y='edss_test_value_numeric')
plt.title('EDSS Values: Active vs Discontinued Patients', fontsize=14, fontweight='bold')
plt.ylabel('EDSS Test Value', fontsize=12)
plt.xlabel('Patient Status', fontsize=12)

# Count plot of patients by EDSS ranges and status
plt.subplot(2, 2, 3)
sns.countplot(data=df_edss, x='edss_range', hue='patient_status',
             palette={'Active': 'lightgreen', 'Discontinued': 'lightcoral'})
plt.title('Patient Count by EDSS Range and Status', fontsize=14, fontweight='bold')
plt.xlabel('EDSS Range', fontsize=12)
plt.ylabel('Number of Patients', fontsize=12)
plt.xticks(rotation=45)
plt.legend(title='Patient Status')

# Mean EDSS Count by Status
plt.subplot(2, 2, 4)
ax = sns.barplot(data=df_edss, x='patient_status', y='edss_test_value_numeric', hue='patient_status',
                palette={'Active': 'lightgreen', 'Discontinued': 'lightcoral'})
plt.title('Mean EDSS Test Values by Patient Status', fontsize=14, fontweight='bold')
plt.xlabel('Patient Status', fontsize=12)
plt.ylabel('Mean EDSS Test Value', fontsize=12)

# Add value labels
for i in ax.containers:
    ax.bar_label(i, fmt='%.2f', padding=3)

plt.tight_layout()
plt.show()


# ### Key Findings
# 
# - **Active patients** have mean EDSS: 2.75
# - **Discontinued patients** have mean EDSS: 3.51
# - Difference in average EDSS: 0.76 points
# - Overall discontinuation rate : **18.7%**
# - **Minimal disability (0-2): 12.1%** discontinuation rate (-6.6 pp vs overall)
# - **Moderate disability (2-4): 16.7%** discontinuation rate (-2.0 pp vs overall)
# - **Severe disability (4-6): 24.6%** discontinuation rate (+5.9 pp vs overall)
# - **Very severe disability (6+): 35.0%** discontinuation rate (+16.3 pp vs overall)

# ### Key Insights
# 
# 1. **Disease Severity Impact**: Patients with higher EDSS scores (indicating greater disability) are significantly more likely to discontinue Ocrevus therapy.
# 
# 2. **Progressive Risk**: Discontinuation risk increases progressively with EDSS severity:
# 
# 3. **Clinical Threshold**: EDSS ≥ 4.0 shows nearly double the discontinuation rate (28.3% vs 15.0%). EDSS score of 4.0 appears to be a critical threshold, with discontinuation rates nearly doubling above this level.

# ## Exploratory Analysis For Prescribed-Ocrevus-Patients

# In[59]:


print(f" Warning: {df['prescribed_ocrevus_pats'].isna().sum()} missing values in prescribed_ocrevus_pats")
# Remove rows with missing prescribed_ocrevus_pats as per requirements
df = df.dropna(subset=['prescribed_ocrevus_pats'])
print(f"✓ Removed rows with missing prescribed_ocrevus_pats. New shape: {df.shape}")


# In[60]:


status_counts = df['patient_status'].value_counts()
for status, count in status_counts.items():
    percentage = (count / len(df)) * 100
    print(f"   • {status}: {count:,} patients ({percentage:.1f}%)")


# In[61]:


print(f" Prescription volume range: {df['prescribed_ocrevus_pats'].min()} - {df['prescribed_ocrevus_pats'].max()}")


# In[62]:


# Display basic statistics of the cleaned EDSS test value feature
df['prescribed_ocrevus_pats'].describe().round(2)


# In[63]:


# Calculate descriptive statistics by patient status
stats_by_status = df.groupby('patient_status')['prescribed_ocrevus_pats'].describe().round(2)
print("Descriptive Statistics for prescribed ocrevus patients count by Patient Status: \n")
print(stats_by_status)


# In[64]:


from scipy.stats import pearsonr, spearmanr

# Correlation Analysis
print(f"\n CORRELATION ANALYSIS:")

# Create binary encoding for correlation (0 = Active, 1 = Discontinued)
df_corr = df.copy()
df_corr['status_binary'] = (df_corr['patient_status'] == 'Discontinued').astype(int)

# Pearson correlation
pearson_r, pearson_p = pearsonr(df_corr['prescribed_ocrevus_pats'], df_corr['status_binary'])
print(f"   • Pearson correlation:  r = {pearson_r:.4f}, p = {pearson_p:.6f}")

# Spearman correlation (rank-based, more robust)
spearman_r, spearman_p = spearmanr(df_corr['prescribed_ocrevus_pats'], df_corr['status_binary'])
print(f"   • Spearman correlation: ρ = {spearman_r:.4f}, p = {spearman_p:.6f}")

# Interpretation
print(f"\n CORRELATION INTERPRETATION:")
if pearson_r > 0:
    direction = "POSITIVE (higher prescription volume → higher discontinuation rate)"
elif pearson_r < 0:
    direction = "NEGATIVE (higher prescription volume → lower discontinuation rate)"
else:
    direction = "NO CORRELATION"

print(f"   • Direction: {direction}")

# Hypothesis validation
print(f"\n HYPOTHESIS VALIDATION:")
if pearson_r < 0 and pearson_p < 0.05:
    print("  HYPOTHESIS SUPPORTED: Higher prescription volumes are associated with lower discontinuation rates")
elif pearson_r > 0 and pearson_p < 0.05:
    print("  HYPOTHESIS CONTRADICTED: Higher prescription volumes are associated with higher discontinuation rates")
else:
    print("  HYPOTHESIS INCONCLUSIVE: No significant relationship found")


# In[65]:


# Create volume categories
df['prescription_volume_category'] = pd.qcut(df['prescribed_ocrevus_pats'], q=4,
                                             labels=['Low (Q1)', 'Medium-Low (Q2)', 'Medium-High (Q3)', 'High (Q4)'])


# In[66]:


df['prescription_volume_category'].value_counts()


# In[67]:


# Calculate discontinuation rates by category
discontinuation_by_category = df.groupby('prescription_volume_category').agg({
    'patient_status': ['count', lambda x: (x == 'Discontinued').sum()],
    'prescribed_ocrevus_pats': 'mean'
}).round(2)


# In[68]:


# Flatten column names
discontinuation_by_category.columns = ['total_patients', 'discontinued_patients','mean_prescriptions']


# In[69]:


# Calculate discontinuation rates
discontinuation_by_category['discontinuation_rate'] = (discontinuation_by_category['discontinued_patients'] / 
                                                       discontinuation_by_category['total_patients'] * 100).round(2)

# Calculate active patients
discontinuation_by_category['active_patients'] = (discontinuation_by_category['total_patients'] - 
                                                  discontinuation_by_category['discontinued_patients'])


# In[70]:


discontinuation_by_category


# ### Key Insight
# - Focus on **mean_prescriptions** and **discontinuation_rate**
# - As prescription volume category changes from low to high, average number of presecriptions increases and discontinuation rates decreases.

# In[71]:


category_rates = df.groupby('prescription_volume_category').apply(lambda x: (x['patient_status'] == 'Discontinued').
                  mean() * 100).reindex(['Low (Q1)', 'Medium-Low (Q2)', 'Medium-High (Q3)', 'High (Q4)'])


# In[72]:


fig = plt.figure(figsize=(20, 16))

# # Histogram of EDSS values by patient status
plt.subplot(2, 2, 1)
plt.hist(df['prescribed_ocrevus_pats'], bins=50, alpha=0.7, color='skyblue', edgecolor='black')
plt.axvline(df['prescribed_ocrevus_pats'].mean(), color='red', linestyle='--', 
            label=f'Mean: {df["prescribed_ocrevus_pats"].mean():.1f}')
plt.axvline(df['prescribed_ocrevus_pats'].median(), color='orange', linestyle='--', 
            label=f'Median: {df["prescribed_ocrevus_pats"].median():.1f}')
plt.title('Overall Distribution of Prescription Volume', fontsize=14, fontweight='bold')
plt.xlabel('Prescribed Ocrevus Patients Count', fontsize=12)
plt.ylabel('Frequency', fontsize=12)
plt.legend()

# Box plot comparing precription volume by patient status
plt.subplot(2, 2, 2)
sns.boxplot(data=df, x='patient_status', y='prescribed_ocrevus_pats')
plt.title('Prescription Volume: Active vs Discontinued Patients', fontsize=14, fontweight='bold')
plt.ylabel('Prescribed Ocrevus Patients Count', fontsize=12)
plt.xlabel('Patient Status', fontsize=12)

# Discontinuation rate by prescription volume category
plt.subplot(2, 2, 3)
ax = sns.barplot(x=category_rates.index, y=category_rates.values, hue=category_rates.index,
                    palette=['red', 'orange', 'yellow', 'green'],alpha=0.7)
plt.title('Discontinuation Rate by Prescription Volume Category', fontsize=14, fontweight='bold')
plt.xlabel('Prescription Volume Category', fontsize=12)
plt.ylabel('Discontinuation Rate (%)', fontsize=12)

# Add value labels
for i in ax.containers:
    ax.bar_label(i, fmt='%.1f%%', padding=3)


# Mean EDSS Count by Status
plt.subplot(2, 2, 4)
ax = sns.barplot(data=df, x='patient_status', y='prescribed_ocrevus_pats', hue='patient_status')
plt.title('Mean Prescription Volume by Patient Status', fontsize=14, fontweight='bold')
plt.xlabel('Patient Status', fontsize=12)
plt.ylabel('Mean Prescription Volume', fontsize=12)

# Add value labels
for i in ax.containers:
    ax.bar_label(i, fmt='%.2f', padding=3)

plt.tight_layout()
plt.show()


# ### Key Findings
# - Overall average physician prescription volume: **113.4 patients**
# - Median physician prescription volume: **93.0 patients**
# - Active patients - Average physician prescription volume: **116.6 patients**
# - Discontinued patients - Average physician prescription volume: **99 patients**
# - Difference (Active - Discontinued): **17.6 patients**
# - Interpretation: **HIGHER** prescription volumes for **active patients**

# ### Key Insights
# #### Original Hypothesis
# - Higher prescription counts by physicians are associated with lower patient discontinuation rates (i.e., patients are more likely to remain active on therapy when treated by physicians with more Ocrevus experience)
# 
# #### Evidence Summary
# - Active patients have physicians with higher prescription volumes (+17.7 patients on average)  
# - Negative correlation (r = -0.0708) supports hypothesis
# - Clear decreasing trend in discontinuation rates across prescription volume quartiles: (-3.05% per quartile) 
# - **Low volume physicians: 22.1% discontinuation rate**
# - **High volume physicians: 12.9% discontinuation rate**
# - 9.2 percentage point improvement from lowest to highest quartile

# ### Days Since First Infusion

# In[ ]:


# Calculate day_since_first_infusion
current_date = pd.Timestamp('2025-06-02')

# Convert firstinfusiondate to datetime if not already
if not pd.api.types.is_datetime64_any_dtype(df['firstinfusiondate']):
    df['firstinfusiondate'] = pd.to_datetime(df['firstinfusiondate'])

df['day_since_first_infusion'] = (current_date - df['firstinfusiondate']).dt.days

# Binning day_since_first_infusion at 180 days
bins = range(0, int(df['day_since_first_infusion'].max()) + 180, 180)
df['days_since_first_infusion_bin'] = pd.cut(df['day_since_first_infusion'], bins=bins, right=False)

# Convert intervals to string labels for better visualization
df['days_since_first_infusion_bin_str'] = df['days_since_first_infusion_bin'].astype(str)

# Prepare data for plotting
count_data = df.groupby(['days_since_first_infusion_bin_str', 'discontinue_flag']).size().reset_index(name='count')

# Calculate percentage data
percentage_data = count_data.copy()
total_per_bin = percentage_data.groupby('days_since_first_infusion_bin_str')['count'].transform('sum')
percentage_data['percentage'] = (percentage_data['count'] / total_per_bin) * 100

# Create proper ordering for the bins (sort by the start value of each interval)
unique_bins = df['days_since_first_infusion_bin'].dropna().unique()
sorted_bins = sorted(unique_bins, key=lambda x: x.left)
bin_order = [str(bin_interval) for bin_interval in sorted_bins]

# Create subplots
fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))

# Chart 1: Count histogram with proper ordering
sns.barplot(data=count_data, x='days_since_first_infusion_bin_str', y='count', 
           hue='discontinue_flag', ax=ax1, palette=['skyblue', 'salmon'],
           order=bin_order)
ax1.set_title('Count of Patients by Days Since First Infusion and Discontinuation Status')
ax1.set_xlabel('Days Since First Infusion (binned at 180 days)')
ax1.set_ylabel('Count of Patients')
ax1.tick_params(axis='x', rotation=45)
handles1, _ = ax1.get_legend_handles_labels()
ax1.legend(handles=handles1, labels=['No (0)', 'Yes (1)'], title='Discontinued')

# Chart 2: Percentage histogram with proper ordering
sns.barplot(data=percentage_data, x='days_since_first_infusion_bin_str', y='percentage', 
           hue='discontinue_flag', ax=ax2, palette=['skyblue', 'salmon'],
           order=bin_order)
ax2.set_title('Percentage Split by Days Since First Infusion and Discontinuation Status')
ax2.set_xlabel('Days Since First Infusion (binned at 180 days)')
ax2.set_ylabel('Percentage of Patients')
ax2.tick_params(axis='x', rotation=45)
handles2, _ = ax2.get_legend_handles_labels()
ax2.legend(handles=handles2, labels=['No (0)', 'Yes (1)'], title='Discontinued')

plt.tight_layout()
plt.show()


# #### Insights
# 
# * From feature days_since_first_infusion, its observed that there are high chances of patient discontinuation based days getting incremented to patient life cycle with ocrevus therapy.

# ### Distribution of Avg Gap between Ocrevus Infusion Days. 

# In[ ]:


df = df[df["avg_infusion_days_gap"].notnull()]

# Binning avg_infusion_days_gap at 30 days
bins = range(0, int(df['avg_infusion_days_gap'].max()) + 30, 30)
df['days_gap_bin'] = pd.cut(df['avg_infusion_days_gap'], bins=bins, right=False)

# Get bin counts and calculate cumulative percentage
bin_counts = df['days_gap_bin'].value_counts().sort_index()
total_count = bin_counts.sum()
cumulative_counts = bin_counts.cumsum()
cumulative_percentage = (cumulative_counts / total_count) * 100

# Convert bin intervals to string labels for plotting
bin_labels = [str(interval) for interval in bin_counts.index]

# Create the dual axis chart
fig, ax1 = plt.subplots(figsize=(12, 6))

# Primary axis - Histogram (Count)
bars = ax1.bar(range(len(bin_counts)), bin_counts.values, 
               color='skyblue', alpha=0.7, label='Count')
ax1.set_xlabel('Days Gap Range (binned at 30 days)')
ax1.set_ylabel('Count of Patients', color='blue')
ax1.tick_params(axis='y', labelcolor='blue')
ax1.set_xticks(range(len(bin_counts)))
ax1.set_xticklabels(bin_labels, rotation=45, ha='right')

# Secondary axis - Cumulative Percentage Line
ax2 = ax1.twinx()
line = ax2.plot(range(len(cumulative_percentage)), cumulative_percentage.values, 
                color='red', marker='o', linewidth=2, markersize=4, label='Cumulative %')
ax2.set_ylabel('Cumulative Percentage (%)', color='red')
ax2.tick_params(axis='y', labelcolor='red')
ax2.set_ylim(0, 100)

# Add title and grid
plt.title('Distribution of Average Infusion Days Gap\n(Histogram with Cumulative Percentage)', 
          fontsize=14, pad=20)
ax1.grid(True, alpha=0.3)

# Add legends
ax1.legend(loc='upper left')
ax2.legend(loc='upper right')

plt.tight_layout()
plt.show()


# #### Key insights:
# 
# * Around 90% of patients infusion days average is within 210 days from previous infusion, with distribution where maximum patients avg gets accumulated between between 180 to 210 days.

# ### Patient Distribution on Avg infusion Days Gap (Segregated on Discontinued flag)

# In[ ]:


df = df[df["avg_infusion_days_gap"].notnull()]

# Binning avg_infusion_days_gap at 180 days
bins = range(0, int(df['avg_infusion_days_gap'].max()) + 90, 90)
df['days_gap_bin'] = pd.cut(df['avg_infusion_days_gap'], bins=bins, right=False)

# Convert intervals to string labels for better visualization
df['days_gap_bin_str'] = df['days_gap_bin'].astype(str)

# Create proper ordering for the bins (sort by the start value of each interval)
unique_bins = df['days_gap_bin'].dropna().unique()
# Sort by the left (start) value of each interval
sorted_bins = sorted(unique_bins, key=lambda x: x.left)
# Convert to string labels in the correct order
bin_order = [str(bin_interval) for bin_interval in sorted_bins]

# Prepare data for plotting
count_data = df.groupby(['days_gap_bin_str', 'discontinue_flag']).size().reset_index(name='count')

# Calculate percentage data
percentage_data = count_data.copy()
total_per_bin = percentage_data.groupby('days_gap_bin_str')['count'].transform('sum')
percentage_data['percentage'] = (percentage_data['count'] / total_per_bin) * 100

# Create subplots
fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))

# Chart 1: Count histogram with proper ordering
sns.barplot(data=count_data, x='days_gap_bin_str', y='count', 
           hue='discontinue_flag', ax=ax1, palette=['skyblue', 'salmon'],
           order=bin_order)  # Use the sorted order
ax1.set_title('Count of Patients by Days Gap Range and Discontinuation Status')
ax1.set_xlabel('Days Gap Range (binned at 180 days)')
ax1.set_ylabel('Count of Patients')
ax1.tick_params(axis='x', rotation=45)
handles1, _ = ax1.get_legend_handles_labels()
ax1.legend(handles=handles1, labels=['No (0)', 'Yes (1)'], title='Discontinued')
# ax1.legend(title='Discontinued', labels=['No (0)', 'Yes (1)'])

# Chart 2: Percentage histogram with proper ordering
sns.barplot(data=percentage_data, x='days_gap_bin_str', y='percentage', 
           hue='discontinue_flag', ax=ax2, palette=['skyblue', 'salmon'],
           order=bin_order)  # Use the sorted order
ax2.set_title('Percentage Split by Days Gap Range and Discontinuation Status')
ax2.set_xlabel('Days Gap Range (binned at 180 days)')
ax2.set_ylabel('Percentage of Patients')
ax2.tick_params(axis='x', rotation=45)
handles2, _ = ax2.get_legend_handles_labels()
ax2.legend(handles=handles2, labels=['No (0)', 'Yes (1)'], title='Discontinued')
# ax2.legend(title='Discontinued', labels=['No (0)', 'Yes (1)'])

plt.tight_layout()
plt.show()


# ### Discontinuation % among patients who received Financial Assisstance

# In[ ]:


# Filtering data for financial_asst_active_flag == 1
df_fa_1 = df[df['financial_asst_active_flag'] == 1]
discontinue_counts_fa_1 = df_fa_1['discontinue_flag'].value_counts(normalize=True) * 100

# Filtering data for financial_asst_active_flag == 0
df_fa_0 = df[df['financial_asst_active_flag'] == 0]
discontinue_counts_fa_0 = df_fa_0['discontinue_flag'].value_counts(normalize=True) * 100

# Plotting pie charts side by side
fig, axes = plt.subplots(1, 2, figsize=(12, 6))

# Set seaborn color palette
sns.set_palette('pastel')
colors = sns.color_palette('pastel')

# Pie chart for financial_asst_active_flag == 1
axes[0].pie(discontinue_counts_fa_1.values, 
           labels=['Continued' if x == 0 else 'Discontinued' for x in discontinue_counts_fa_1.index], 
           autopct='%1.1f%%', 
           startangle=90,
           colors=colors)
axes[0].set_title('Discontinuation % (Financial Assistance Received)')

# Pie chart for financial_asst_active_flag == 0
axes[1].pie(discontinue_counts_fa_0.values, 
           labels=['Continued' if x == 0 else 'Discontinued' for x in discontinue_counts_fa_0.index], 
           autopct='%1.1f%%', 
           startangle=90,
           colors=colors)
axes[1].set_title('Discontinuation % (Financial Assistance Not Received)')

plt.tight_layout()
plt.show()


#  * There are quite high chances of patient discontinuing from Ocrevus therapy who haven't received financial assisstnace compared to ones who have.
