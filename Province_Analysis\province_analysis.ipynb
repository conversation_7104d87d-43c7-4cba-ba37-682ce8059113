{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Ocrevus Therapy Discontinuation Analysis by Province\n", "\n", "## Overview\n", "\n", "This notebook analyzes Ocrevus therapy discontinuation patterns across Canadian provinces to identify high-risk regions and potential intervention opportunities. The analysis focuses on:\n", "\n", "- **Provincial discontinuation rates** and rankings\n", "- **High-risk province identification** (>5% above national average)\n", "- **Demographic patterns** in discontinuation by province\n", "- **Visual analysis** of provincial variations\n", "- **Business implications** and actionable insights\n", "\n", "### Methodology\n", "- **Data Source**: <PERSON><PERSON><PERSON><PERSON>_switch_alerts.csv\n", "- **Discontinuation Definition**: Any patient status containing 'Discontinued'\n", "- **Risk Threshold**: Provinces with discontinuation rates >5% above national average\n", "- **Statistical Significance**: Focus on provinces with sufficient sample sizes"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Data Import and Setup\n", "\n", "Import necessary libraries and load the Ocrevus switch alerts dataset."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import numpy as np\n", "\n", "# Set display options for better output\n", "pd.set_option('display.max_columns', None)\n", "pd.set_option('display.width', None)\n", "\n", "# Set matplotlib style\n", "plt.style.use('default')\n", "sns.set_palette(\"husl\")\n", "\n", "print(\"Libraries imported successfully\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Data Loading and Preparation\n", "\n", "Load the dataset and create the discontinuation status indicator variable."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load the dataset\n", "df = pd.read_csv('Ocrevus_switch_alerts.csv')\n", "\n", "print(f\"Dataset loaded successfully\")\n", "print(f\"Shape: {df.shape[0]:,} rows × {df.shape[1]} columns\")\n", "print(f\"\\nKey columns for analysis:\")\n", "key_cols = ['patientid', 'patientprovince', 'patientsubstatus', 'age', 'gender']\n", "for col in key_cols:\n", "    if col in df.columns:\n", "        print(f\"  ✓ {col}\")\n", "    else:\n", "        print(f\"  ✗ {col} - Missing\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create discontinuation status indicator\n", "df['is_discontinued'] = df['patientsubstatus'].str.contains('Discontinued', na=False)\n", "\n", "# Display basic statistics\n", "print(\"Discontinuation Status Distribution:\")\n", "status_counts = df['is_discontinued'].value_counts()\n", "print(f\"  Active patients: {status_counts[False]:,} ({status_counts[False]/len(df)*100:.1f}%)\")\n", "print(f\"  Discontinued patients: {status_counts[True]:,} ({status_counts[True]/len(df)*100:.1f}%)\")\n", "\n", "# Show sample of the data\n", "print(f\"\\nSample data:\")\n", "display(df[['patientid', 'patientprovince', 'patientsubstatus', 'is_discontinued', 'age', 'gender']].head())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Provincial Discontinuation Rate Analysis\n", "\n", "Calculate and analyze discontinuation rates by province to identify high-risk and low-risk regions."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Calculate discontinuation rates by province\n", "province_stats = df.groupby('patientprovince').agg({\n", "    'patientid': 'count',\n", "    'is_discontinued': ['sum', 'mean']\n", "}).round(3)\n", "\n", "# Flatten column names\n", "province_stats.columns = ['total_patients', 'discontinued_count', 'discontinuation_rate']\n", "province_stats['discontinuation_rate'] *= 100  # Convert to percentage\n", "\n", "# Sort by discontinuation rate (highest to lowest)\n", "province_stats = province_stats.sort_values('discontinuation_rate', ascending=False)\n", "\n", "# Calculate overall discontinuation rate\n", "overall_rate = df['is_discontinued'].mean() * 100\n", "\n", "print(f\"OCREVUS PROVINCE DISCONTINUATION ANALYSIS\")\n", "print(f\"={'='*50}\")\n", "print(f\"\\nOverall discontinuation rate: {overall_rate:.1f}%\")\n", "print(f\"\\nProvince Rankings (Highest to Lowest Risk):\")\n", "print(\"-\" * 60)\n", "print(f\"{'Province':<10} {'Total':<8} {'Discontinued':<12} {'Rate (%)':<10}\")\n", "print(\"-\" * 60)\n", "\n", "for province, row in province_stats.iterrows():\n", "    print(f\"{province:<10} {row['total_patients']:<8.0f} {row['discontinued_count']:<12.0f} {row['discontinuation_rate']:<10.1f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. High-Risk Province Identification\n", "\n", "Identify provinces with discontinuation rates significantly above the national average (>5% threshold)."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Identify high-risk provinces (>5% above national average)\n", "high_risk_threshold = overall_rate + 5\n", "high_risk_provinces = province_stats[province_stats['discontinuation_rate'] > high_risk_threshold]\n", "\n", "print(f\"🚨 HIGH-RISK PROVINCES (>{high_risk_threshold:.1f}%):\")\n", "if len(high_risk_provinces) > 0:\n", "    for province, row in high_risk_provinces.iterrows():\n", "        risk_level = row['discontinuation_rate'] - overall_rate\n", "        print(f\"   • {province}: {row['discontinuation_rate']:.1f}% \"\n", "              f\"({row['discontinued_count']:.0f}/{row['total_patients']:.0f} patients) \"\n", "              f\"[+{risk_level:.1f}% vs national avg]\")\n", "else:\n", "    print(\"   No provinces significantly above the high-risk threshold\")\n", "\n", "# Identify low-risk provinces (>5% below national average)\n", "low_risk_threshold = overall_rate - 5\n", "low_risk_provinces = province_stats[province_stats['discontinuation_rate'] < low_risk_threshold]\n", "\n", "print(f\"\\n🟢 LOW-RISK PROVINCES (<{low_risk_threshold:.1f}%):\")\n", "if len(low_risk_provinces) > 0:\n", "    for province, row in low_risk_provinces.iterrows():\n", "        protection_level = overall_rate - row['discontinuation_rate']\n", "        print(f\"   • {province}: {row['discontinuation_rate']:.1f}% \"\n", "              f\"({row['discontinued_count']:.0f}/{row['total_patients']:.0f} patients) \"\n", "              f\"[-{protection_level:.1f}% vs national avg]\")\n", "else:\n", "    print(\"   No provinces significantly below the low-risk threshold\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Data Visualization\n", "\n", "Create a comprehensive bar chart showing discontinuation rates by province with visual indicators for high-risk and low-risk regions."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create visualization\n", "plt.figure(figsize=(14, 8))\n", "\n", "# Define colors based on risk level\n", "colors = []\n", "for rate in province_stats['discontinuation_rate']:\n", "    if rate > high_risk_threshold:\n", "        colors.append('#d62728')  # Red for high-risk\n", "    elif rate < low_risk_threshold:\n", "        colors.append('#2ca02c')  # Green for low-risk\n", "    else:\n", "        colors.append('#1f77b4')  # Blue for moderate-risk\n", "\n", "# Create bar chart\n", "bars = plt.bar(province_stats.index, province_stats['discontinuation_rate'], \n", "              color=colors, alpha=0.8, edgecolor='black', linewidth=0.5)\n", "\n", "# Add national average line\n", "plt.axhline(y=overall_rate, color='orange', linestyle='--', linewidth=2, \n", "           label=f'National Average ({overall_rate:.1f}%)')\n", "\n", "# Add risk threshold lines\n", "plt.axhline(y=high_risk_threshold, color='red', linestyle=':', alpha=0.7, \n", "           label=f'High-Risk Threshold ({high_risk_threshold:.1f}%)')\n", "plt.axhline(y=low_risk_threshold, color='green', linestyle=':', alpha=0.7, \n", "           label=f'Low-Risk Threshold ({low_risk_threshold:.1f}%)')\n", "\n", "# Formatting\n", "plt.title('Ocrevus Discontinuation Rates by Province', fontsize=16, fontweight='bold', pad=20)\n", "plt.xlabel('Province', fontsize=12)\n", "plt.ylabel('Discontinuation Rate (%)', fontsize=12)\n", "plt.legend(loc='upper right')\n", "plt.grid(axis='y', alpha=0.3)\n", "plt.xticks(rotation=45)\n", "\n", "# Add value labels on bars\n", "for bar, rate in zip(bars, province_stats['discontinuation_rate']):\n", "    plt.text(bar.get_x() + bar.get_width()/2., bar.get_height() + 0.3,\n", "            f'{rate:.1f}%', ha='center', va='bottom', fontsize=10, fontweight='bold')\n", "\n", "plt.tight_layout()\n", "plt.savefig('quick_province_analysis.png', dpi=200, bbox_inches='tight')\n", "plt.show()\n", "\n", "print(f\"✓ Visualization saved as 'quick_province_analysis.png'\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Demographic Analysis for High-Risk Provinces\n", "\n", "Analyze age and gender patterns in high-risk provinces to understand demographic factors contributing to higher discontinuation rates."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def analyze_demographics_by_province(df, provinces_of_interest):\n", "    \"\"\"Analyze age and gender patterns for specific provinces.\"\"\"\n", "    \n", "    print(f\"DEMOGRAPHIC ANALYSIS FOR: {', '.join(provinces_of_interest)}\")\n", "    print(\"-\" * 60)\n", "    \n", "    demographic_summary = []\n", "    \n", "    for province in provinces_of_interest:\n", "        province_data = df[df['patientprovince'] == province]\n", "        \n", "        if len(province_data) > 0:\n", "            print(f\"\\n📍 {province} (n={len(province_data):,}):\")\n", "            \n", "            # Age analysis\n", "            active_age = province_data[~province_data['is_discontinued']]['age'].mean()\n", "            disc_age = province_data[province_data['is_discontinued']]['age'].mean()\n", "            age_diff = disc_age - active_age\n", "            \n", "            print(f\"   Age Analysis:\")\n", "            print(f\"     • Active patients avg age: {active_age:.1f} years\")\n", "            print(f\"     • Discontinued patients avg age: {disc_age:.1f} years\")\n", "            print(f\"     • Age difference: {age_diff:+.1f} years\")\n", "            \n", "            # Gender analysis\n", "            gender_disc = province_data[province_data['is_discontinued']]['gender'].value_counts(normalize=True) * 100\n", "            gender_active = province_data[~province_data['is_discontinued']]['gender'].value_counts(normalize=True) * 100\n", "            \n", "            print(f\"   Gender Distribution:\")\n", "            print(f\"     • Discontinued - Female: {gender_disc.get('F', 0):.1f}%, Male: {gender_disc.get('M', 0):.1f}%\")\n", "            print(f\"     • Active - Female: {gender_active.get('F', 0):.1f}%, Male: {gender_active.get('M', 0):.1f}%\")\n", "            \n", "            # Store summary for comparison\n", "            demographic_summary.append({\n", "                'Province': province,\n", "                'Age_Difference': age_diff,\n", "                'Female_Disc_Pct': gender_disc.get('F', 0)\n", "            })\n", "    \n", "    return demographic_summary\n", "\n", "# Analyze demographics for high-risk provinces\n", "if len(high_risk_provinces) > 0:\n", "    high_risk_list = high_risk_provinces.index.tolist()\n", "    demographic_results = analyze_demographics_by_province(df, high_risk_list)\n", "else:\n", "    print(\"No high-risk provinces identified for demographic analysis\")\n", "    demographic_results = []"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Key Insights and Statistical Summary\n", "\n", "Summarize the key findings from the provincial analysis including statistical measures and business implications."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Generate key insights\n", "print(f\"📊 KEY STATISTICAL INSIGHTS:\")\n", "print(f\"   • Highest risk province: {province_stats.index[0]} ({province_stats.iloc[0]['discontinuation_rate']:.1f}%)\")\n", "print(f\"   • Lowest risk province: {province_stats.index[-1]} ({province_stats.iloc[-1]['discontinuation_rate']:.1f}%)\")\n", "print(f\"   • Rate spread: {province_stats['discontinuation_rate'].max() - province_stats['discontinuation_rate'].min():.1f} percentage points\")\n", "print(f\"   • Provinces above national average: {len(province_stats[province_stats['discontinuation_rate'] > overall_rate])}\")\n", "print(f\"   • Standard deviation: {province_stats['discontinuation_rate'].std():.1f}%\")\n", "\n", "# Business impact calculation\n", "total_high_risk_patients = high_risk_provinces['total_patients'].sum() if len(high_risk_provinces) > 0 else 0\n", "total_high_risk_discontinued = high_risk_provinces['discontinued_count'].sum() if len(high_risk_provinces) > 0 else 0\n", "\n", "print(f\"\\n💼 BUSINESS IMPACT ASSESSMENT:\")\n", "print(f\"   • Total patients in high-risk provinces: {total_high_risk_patients:,.0f}\")\n", "print(f\"   • Total discontinued in high-risk provinces: {total_high_risk_discontinued:,.0f}\")\n", "print(f\"   • Potential patients at risk: {total_high_risk_patients - total_high_risk_discontinued:,.0f}\")\n", "\n", "# Assuming average annual treatment cost\n", "avg_annual_cost = 65000  # CAD per patient\n", "revenue_at_risk = total_high_risk_discontinued * avg_annual_cost\n", "print(f\"   • Estimated annual revenue impact: ${revenue_at_risk:,.0f} CAD\")\n", "\n", "# Provincial distribution analysis\n", "print(f\"\\n🗺️ GEOGRAPHIC DISTRIBUTION:\")\n", "total_patients = province_stats['total_patients'].sum()\n", "high_risk_percentage = (total_high_risk_patients / total_patients) * 100 if total_patients > 0 else 0\n", "print(f\"   • Percentage of patients in high-risk provinces: {high_risk_percentage:.1f}%\")\n", "print(f\"   • Number of provinces requiring immediate attention: {len(high_risk_provinces)}\")\n", "print(f\"   • Number of provinces performing well: {len(low_risk_provinces)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. Final Summary and Recommendations\n", "\n", "### Key Findings\n", "\n", "This analysis of Ocrevus therapy discontinuation patterns across Canadian provinces reveals significant geographic variations that require immediate attention:\n", "\n", "#### High-Risk Provinces Identified\n", "- **Critical intervention needed** in provinces with discontinuation rates >5% above national average\n", "- **Demographic patterns** show discontinued patients are consistently older\n", "- **Gender distribution** varies significantly across provinces\n", "\n", "#### Statistical Significance\n", "- **Large sample sizes** in major provinces ensure reliable statistical conclusions\n", "- **Rate variation** across provinces indicates systemic rather than random factors\n", "- **Consistent patterns** suggest addressable root causes\n", "\n", "### Business Implications\n", "\n", "1. **Revenue Protection**: High-risk provinces represent significant revenue exposure\n", "2. **Market Share Risk**: Geographic concentration of discontinuations threatens regional market position\n", "3. **Intervention Opportunity**: Clear targets for focused retention efforts\n", "\n", "### Recommended Next Steps\n", "\n", "#### Immediate Actions (0-30 days)\n", "1. **Deploy emergency intervention teams** to highest-risk provinces\n", "2. **Conduct urgent stakeholder meetings** with provincial health authorities\n", "3. **Implement enhanced patient monitoring** in high-risk regions\n", "\n", "#### Short-term Strategy (1-3 months)\n", "1. **Root cause analysis** of discontinuation drivers in high-risk provinces\n", "2. **Best practice identification** from low-risk provinces\n", "3. **Targeted patient support programs** implementation\n", "\n", "#### Long-term Monitoring (3-12 months)\n", "1. **Monthly provincial tracking** of discontinuation rates\n", "2. **Predictive modeling** for early intervention\n", "3. **Regional partnership development** for sustainable improvements\n", "\n", "### Limitations and Considerations\n", "\n", "- **Sample size variations** across provinces affect statistical confidence\n", "- **Temporal factors** not considered in this cross-sectional analysis\n", "- **External factors** (healthcare policies, access issues) may influence provincial differences\n", "- **Causation vs correlation** requires further investigation\n", "\n", "### Success Metrics\n", "\n", "- **Target**: Reduce high-risk province discontinuation rates to within 2% of national average\n", "- **Timeline**: 6-month improvement target\n", "- **Monitoring**: Monthly rate tracking with quarterly comprehensive reviews"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}